<?php
ArContactUsLoader::loadController('ArContractUsControllerAbstract');
ArContactUsLoader::loadClass('ArContactUsAntiSpam');

class ArContactUsAntiSpamController extends ArContractUsControllerAbstract
{
    protected function ajaxActions()
    {
        return array(
            'arcontactus_block_ip' => 'blockIP',
            'arcontactus_unblock_ip' => 'unblockIP',
            'arcontactus_get_blocked_ips' => 'getBlockedIPs',
            'arcontactus_clean_rate_limit_data' => 'cleanRateLimitData'
        );
    }
    
    public function blockIP()
    {
        $this->assertAccess();
        
        $ip = sanitize_text_field($_POST['ip']);
        $reason = sanitize_text_field($_POST['reason']);
        
        if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
            wp_die($this->returnJson(array(
                'success' => 0,
                'message' => __('Invalid IP address', 'ar-contactus')
            )));
        }
        
        ArContactUsAntiSpam::blockIP($ip, $reason);
        
        wp_die($this->returnJson(array(
            'success' => 1,
            'message' => __('IP address blocked successfully', 'ar-contactus')
        )));
    }
    
    public function unblockIP()
    {
        $this->assertAccess();
        
        $ip = sanitize_text_field($_POST['ip']);
        
        if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
            wp_die($this->returnJson(array(
                'success' => 0,
                'message' => __('Invalid IP address', 'ar-contactus')
            )));
        }
        
        ArContactUsAntiSpam::unblockIP($ip);
        
        wp_die($this->returnJson(array(
            'success' => 1,
            'message' => __('IP address unblocked successfully', 'ar-contactus')
        )));
    }
    
    public function getBlockedIPs()
    {
        $this->assertAccess();
        
        $blockedIPs = ArContactUsAntiSpam::getBlockedIPs();
        
        wp_die($this->returnJson(array(
            'success' => 1,
            'data' => $blockedIPs
        )));
    }
    
    public function cleanRateLimitData()
    {
        $this->assertAccess();
        
        ArContactUsAntiSpam::cleanOldData();
        
        wp_die($this->returnJson(array(
            'success' => 1,
            'message' => __('Rate limit data cleaned successfully', 'ar-contactus')
        )));
    }
}
