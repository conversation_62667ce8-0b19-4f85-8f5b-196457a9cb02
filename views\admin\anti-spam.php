<div class="ui container">
    <h2><?php echo __('Anti-Spam Settings', 'ar-contactus') ?></h2>
    
    <div class="ui grid">
        <div class="sixteen wide column">
            <div class="ui segment">
                <h3><?php echo __('Blocked IP Addresses', 'ar-contactus') ?></h3>
                
                <div class="ui form">
                    <div class="fields">
                        <div class="ten wide field">
                            <label><?php echo __('IP Address', 'ar-contactus') ?></label>
                            <input type="text" id="block-ip-input" placeholder="<?php echo __('Enter IP address to block', 'ar-contactus') ?>">
                        </div>
                        <div class="six wide field">
                            <label><?php echo __('Reason', 'ar-contactus') ?></label>
                            <input type="text" id="block-reason-input" placeholder="<?php echo __('Reason for blocking', 'ar-contactus') ?>">
                        </div>
                    </div>
                    <button class="ui primary button" onclick="blockIP()"><?php echo __('Block IP', 'ar-contactus') ?></button>
                    <button class="ui button" onclick="loadBlockedIPs()"><?php echo __('Refresh List', 'ar-contactus') ?></button>
                    <button class="ui orange button" onclick="cleanRateLimitData()"><?php echo __('Clean Old Data', 'ar-contactus') ?></button>
                </div>
                
                <div class="ui divider"></div>
                
                <table class="ui celled table" id="blocked-ips-table">
                    <thead>
                        <tr>
                            <th><?php echo __('IP Address', 'ar-contactus') ?></th>
                            <th><?php echo __('Blocked At', 'ar-contactus') ?></th>
                            <th><?php echo __('Reason', 'ar-contactus') ?></th>
                            <th><?php echo __('Actions', 'ar-contactus') ?></th>
                        </tr>
                    </thead>
                    <tbody id="blocked-ips-tbody">
                        <!-- Blocked IPs will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="ui grid">
        <div class="sixteen wide column">
            <div class="ui segment">
                <h3><?php echo __('Anti-Spam Features', 'ar-contactus') ?></h3>
                
                <div class="ui list">
                    <div class="item">
                        <i class="checkmark icon green"></i>
                        <div class="content">
                            <div class="header"><?php echo __('Vietnamese Phone Validation', 'ar-contactus') ?></div>
                            <div class="description"><?php echo __('Only accepts valid Vietnamese phone numbers', 'ar-contactus') ?></div>
                        </div>
                    </div>
                    <div class="item">
                        <i class="checkmark icon green"></i>
                        <div class="content">
                            <div class="header"><?php echo __('Honeypot Field', 'ar-contactus') ?></div>
                            <div class="description"><?php echo __('Hidden field to catch automated bots', 'ar-contactus') ?></div>
                        </div>
                    </div>
                    <div class="item">
                        <i class="checkmark icon green"></i>
                        <div class="content">
                            <div class="header"><?php echo __('Rate Limiting', 'ar-contactus') ?></div>
                            <div class="description"><?php echo __('Limits submissions to 5 per hour and 20 per day per IP', 'ar-contactus') ?></div>
                        </div>
                    </div>
                    <div class="item">
                        <i class="checkmark icon green"></i>
                        <div class="content">
                            <div class="header"><?php echo __('Auto IP Blocking', 'ar-contactus') ?></div>
                            <div class="description"><?php echo __('Automatically blocks IPs after 10 failed attempts in an hour', 'ar-contactus') ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function blockIP() {
    var ip = document.getElementById('block-ip-input').value;
    var reason = document.getElementById('block-reason-input').value;
    
    if (!ip) {
        alert('<?php echo __('Please enter an IP address', 'ar-contactus') ?>');
        return;
    }
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'arcontactus_block_ip',
            ip: ip,
            reason: reason,
            _wpnonce: '<?php echo wp_create_nonce('arcu_admin') ?>'
        },
        success: function(response) {
            if (response.success) {
                alert(response.message);
                document.getElementById('block-ip-input').value = '';
                document.getElementById('block-reason-input').value = '';
                loadBlockedIPs();
            } else {
                alert(response.message);
            }
        }
    });
}

function unblockIP(ip) {
    if (!confirm('<?php echo __('Are you sure you want to unblock this IP?', 'ar-contactus') ?>')) {
        return;
    }
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'arcontactus_unblock_ip',
            ip: ip,
            _wpnonce: '<?php echo wp_create_nonce('arcu_admin') ?>'
        },
        success: function(response) {
            if (response.success) {
                alert(response.message);
                loadBlockedIPs();
            } else {
                alert(response.message);
            }
        }
    });
}

function loadBlockedIPs() {
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'arcontactus_get_blocked_ips',
            _wpnonce: '<?php echo wp_create_nonce('arcu_admin') ?>'
        },
        success: function(response) {
            if (response.success) {
                var tbody = document.getElementById('blocked-ips-tbody');
                tbody.innerHTML = '';
                
                for (var ip in response.data) {
                    var data = response.data[ip];
                    var row = document.createElement('tr');
                    var date = new Date(data.blocked_at * 1000);
                    
                    row.innerHTML = '<td>' + ip + '</td>' +
                                   '<td>' + date.toLocaleString() + '</td>' +
                                   '<td>' + (data.reason || '<?php echo __('No reason provided', 'ar-contactus') ?>') + '</td>' +
                                   '<td><button class="ui red button small" onclick="unblockIP(\'' + ip + '\')"><?php echo __('Unblock', 'ar-contactus') ?></button></td>';
                    
                    tbody.appendChild(row);
                }
                
                if (Object.keys(response.data).length === 0) {
                    var row = document.createElement('tr');
                    row.innerHTML = '<td colspan="4" class="center aligned"><?php echo __('No blocked IPs', 'ar-contactus') ?></td>';
                    tbody.appendChild(row);
                }
            }
        }
    });
}

function cleanRateLimitData() {
    if (!confirm('<?php echo __('Are you sure you want to clean old rate limit data?', 'ar-contactus') ?>')) {
        return;
    }
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'arcontactus_clean_rate_limit_data',
            _wpnonce: '<?php echo wp_create_nonce('arcu_admin') ?>'
        },
        success: function(response) {
            if (response.success) {
                alert(response.message);
            } else {
                alert(response.message);
            }
        }
    });
}

// Load blocked IPs on page load
jQuery(document).ready(function() {
    loadBlockedIPs();
});
</script>
