<?php
ArContactUsLoader::loadModel('ArContactUsConfigModelAbstract');

abstract class ArContactUsConfigModel extends ArContactUsConfigModelAbstract
{
    public static function getIcons()
    {
        return array(
            'facebook-messenger' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224 32C15.9 32-77.5 278 84.6 400.6V480l75.7-42c142.2 39.8 285.4-59.9 285.4-198.7C445.8 124.8 346.5 32 224 32zm23.4 278.1L190 250.5 79.6 311.6l121.1-128.5 57.4 59.6 110.4-61.1-121.1 128.5z"></path></svg>',
            'facebook' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M448 56.7v398.5c0 13.7-11.1 24.7-24.7 24.7H309.1V306.5h58.2l8.7-67.6h-67v-43.2c0-19.6 5.4-32.9 33.5-32.9h35.8v-60.5c-6.2-.8-27.4-2.7-52.2-2.7-51.6 0-87 31.5-87 89.4v49.9h-58.4v67.6h58.4V480H24.7C11.1 480 0 468.9 0 455.3V56.7C0 43.1 11.1 32 24.7 32h398.5c13.7 0 24.8 11.1 24.8 24.7z"></path></svg>',
            'facebook-f' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 264 512"><path fill="currentColor" d="M76.7 512V283H0v-91h76.7v-71.7C76.7 42.4 124.3 0 193.8 0c33.3 0 61.9 2.5 70.2 3.6V85h-48.2c-37.8 0-45.1 18-45.1 44.3V192H256l-11.7 91h-73.6v229"></path></svg>',
            'viber' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M444 49.9C431.3 38.2 379.9.9 265.3.4c0 0-135.1-8.1-200.9 52.3C27.8 89.3 14.9 143 13.5 209.5c-1.4 66.5-3.1 191.1 117 224.9h.1l-.1 51.6s-.8 20.9 13 25.1c16.6 5.2 26.4-10.7 42.3-27.8 8.7-9.4 20.7-23.2 29.8-33.7 82.2 6.9 145.3-8.9 152.5-11.2 16.6-5.4 110.5-17.4 125.7-142 15.8-128.6-7.6-209.8-49.8-246.5zM457.9 287c-12.9 104-89 110.6-103 115.1-6 1.9-61.5 15.7-131.2 11.2 0 0-52 62.7-68.2 79-5.3 5.3-11.1 4.8-11-5.7 0-6.9.4-85.7.4-85.7-.1 0-.1 0 0 0-101.8-28.2-95.8-134.3-94.7-189.8 1.1-55.5 11.6-101 42.6-131.6 55.7-50.5 170.4-43 170.4-43 96.9.4 143.3 29.6 154.1 39.4 35.7 30.6 53.9 103.8 40.6 211.1zm-139-80.8c.4 8.6-12.5 9.2-12.9.6-1.1-22-11.4-32.7-32.6-33.9-8.6-.5-7.8-13.4.7-12.9 27.9 1.5 43.4 17.5 44.8 46.2zm20.3 11.3c1-42.4-25.5-75.6-75.8-79.3-8.5-.6-7.6-13.5.9-12.9 58 4.2 88.9 44.1 87.8 92.5-.1 8.6-13.1 8.2-12.9-.3zm47 13.4c.1 8.6-12.9 8.7-12.9.1-.6-81.5-54.9-125.9-120.8-126.4-8.5-.1-8.5-12.9 0-12.9 73.7.5 133 51.4 133.7 139.2zM374.9 329v.2c-10.8 19-31 40-51.8 33.3l-.2-.3c-21.1-5.9-70.8-31.5-102.2-56.5-16.2-12.8-31-27.9-42.4-42.4-10.3-12.9-20.7-28.2-30.8-46.6-21.3-38.5-26-55.7-26-55.7-6.7-20.8 14.2-41 33.3-51.8h.2c9.2-4.8 18-3.2 23.9 3.9 0 0 12.4 14.8 17.7 22.1 5 6.8 11.7 17.7 15.2 23.8 6.1 10.9 2.3 22-3.7 26.6l-12 9.6c-6.1 4.9-5.3 14-5.3 14s17.8 67.3 84.3 84.3c0 0 9.1.8 14-5.3l9.6-12c4.6-6 15.7-9.8 26.6-3.7 14.7 8.3 33.4 21.2 45.8 32.9 7 5.7 8.6 14.4 3.8 23.6z"></path></svg>',
            'telegram-plane' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M446.7 98.6l-67.6 318.8c-5.1 22.5-18.4 28.1-37.3 17.5l-103-75.9-49.7 47.8c-5.5 5.5-10.1 10.1-20.7 10.1l7.4-104.9 190.9-172.5c8.3-7.4-1.8-11.5-12.9-4.1L117.8 284 16.2 252.2c-22.1-6.9-22.5-22.1 4.6-32.7L418.2 66.4c18.4-6.9 34.5 4.1 28.5 32.2z"></path></svg>',
            'skype' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.7 299.8c2.9-14 4.7-28.9 4.7-43.8 0-113.5-91.9-205.3-205.3-205.3-14.9 0-29.7 1.7-43.8 4.7C161.3 40.7 137.7 32 112 32 50.2 32 0 82.2 0 144c0 25.7 8.7 49.3 23.3 68.2-2.9 14-4.7 28.9-4.7 43.8 0 113.5 91.9 205.3 205.3 205.3 14.9 0 29.7-1.7 43.8-4.7 19 14.6 42.6 23.3 68.2 23.3 61.8 0 112-50.2 112-112 .1-25.6-8.6-49.2-23.2-68.1zm-194.6 91.5c-65.6 0-120.5-29.2-120.5-65 0-16 9-30.6 29.5-30.6 31.2 0 34.1 44.9 88.1 44.9 25.7 0 42.3-11.4 42.3-26.3 0-18.7-16-21.6-42-28-62.5-15.4-117.8-22-117.8-87.2 0-59.2 58.6-81.1 109.1-81.1 55.1 0 110.8 21.9 110.8 55.4 0 16.9-11.4 31.8-30.3 31.8-28.3 0-29.2-33.5-75-33.5-25.7 0-42 7-42 22.5 0 19.8 20.8 21.8 69.1 33 41.4 9.3 90.7 26.8 90.7 77.6 0 59.1-57.1 86.5-112 86.5z"></path></svg>',
            'envelope' => '<svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M464 64H48C21.5 64 0 85.5 0 112v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM48 96h416c8.8 0 16 7.2 16 16v41.4c-21.9 18.5-53.2 44-150.6 121.3-16.9 13.4-50.2 45.7-73.4 45.3-23.2.4-56.6-31.9-73.4-45.3C85.2 197.4 53.9 171.9 32 153.4V112c0-8.8 7.2-16 16-16zm416 320H48c-8.8 0-16-7.2-16-16V195c22.8 18.7 58.8 47.6 130.7 104.7 20.5 16.4 56.7 52.5 93.3 52.3 36.4.3 72.3-35.5 93.3-52.3 71.9-57.1 107.9-86 130.7-104.7v205c0 8.8-7.2 16-16 16z"></path></svg>',
            'phone' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"></path></svg>',
            'whatsapp' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"></path></svg>',
            'twitter' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"></path></svg>',
            'odnoklassniki' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M275.1 334c-27.4 17.4-65.1 24.3-90 26.9l20.9 20.6 76.3 76.3c27.9 28.6-17.5 73.3-45.7 45.7-19.1-19.4-47.1-47.4-76.3-76.6L84 503.4c-28.2 27.5-73.6-17.6-45.4-45.7 19.4-19.4 47.1-47.4 76.3-76.3l20.6-20.6c-24.6-2.6-62.9-9.1-90.6-26.9-32.6-21-46.9-33.3-34.3-59 7.4-14.6 27.7-26.9 54.6-5.7 0 0 36.3 28.9 94.9 28.9s94.9-28.9 94.9-28.9c26.9-21.1 47.1-8.9 54.6 5.7 12.4 25.7-1.9 38-34.5 59.1zM30.3 129.7C30.3 58 88.6 0 160 0s129.7 58 129.7 129.7c0 71.4-58.3 129.4-129.7 129.4s-129.7-58-129.7-129.4zm66 0c0 35.1 28.6 63.7 63.7 63.7s63.7-28.6 63.7-63.7c0-35.4-28.6-64-63.7-64s-63.7 28.6-63.7 64z"></path></svg>',
            'vk' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M545 117.7c3.7-12.5 0-21.7-17.8-21.7h-58.9c-15 0-21.9 7.9-25.6 16.7 0 0-30 73.1-72.4 120.5-13.7 13.7-20 18.1-27.5 18.1-3.7 0-9.4-4.4-9.4-16.9V117.7c0-15-4.2-21.7-16.6-21.7h-92.6c-9.4 0-15 7-15 13.5 0 14.2 21.2 17.5 23.4 57.5v86.8c0 19-3.4 22.5-10.9 22.5-20 0-68.6-73.4-97.4-157.4-5.8-16.3-11.5-22.9-26.6-22.9H38.8c-16.8 0-20.2 7.9-20.2 16.7 0 15.6 20 93.1 93.1 195.5C160.4 378.1 229 416 291.4 416c37.5 0 42.1-8.4 42.1-22.9 0-66.8-3.4-73.1 15.4-73.1 8.7 0 23.7 4.4 58.7 38.1 40 40 46.6 57.9 69 57.9h58.9c16.8 0 25.3-8.4 20.4-25-11.2-34.9-86.9-106.7-90.3-111.5-8.7-11.2-6.2-16.2 0-26.2.1-.1 72-101.3 79.4-135.6z"></path></svg>',
            'slack-hash' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M446.2 270.4c-6.2-19-26.9-29.1-46-22.9l-45.4 15.1-30.3-90 45.4-15.1c19.1-6.2 29.1-26.8 23-45.9-6.2-19-26.9-29.1-46-22.9l-45.4 15.1-15.7-47c-6.2-19-26.9-29.1-46-22.9-19.1 6.2-29.1 26.8-23 45.9l15.7 47-93.4 31.2-15.7-47c-6.2-19-26.9-29.1-46-22.9-19.1 6.2-29.1 26.8-23 45.9l15.7 47-45.3 15c-19.1 6.2-29.1 26.8-23 45.9 5 14.5 19.1 24 33.6 24.6 6.8 1 12-1.6 57.7-16.8l30.3 90L78 354.8c-19 6.2-29.1 26.9-23 45.9 5 14.5 19.1 24 33.6 24.6 6.8 1 12-1.6 57.7-16.8l15.7 47c5.9 16.9 24.7 29 46 22.9 19.1-6.2 29.1-26.8 23-45.9l-15.7-47 93.6-31.3 15.7 47c5.9 16.9 24.7 29 46 22.9 19.1-6.2 29.1-26.8 23-45.9l-15.7-47 45.4-15.1c19-6 29.1-26.7 22.9-45.7zm-254.1 47.2l-30.3-90.2 93.5-31.3 30.3 90.2-93.5 31.3z"></path></svg>',
            'intercom' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 32"><path style="fill: currentColor" d="M28,32 C28,32 23.2863266,30.1450667 19.4727818,28.6592 L3.43749107,28.6592 C1.53921989,28.6592 0,27.0272 0,25.0144 L0,3.6448 C0,1.632 1.53921989,0 3.43749107,0 L24.5615088,0 C26.45978,0 27.9989999,1.632 27.9989999,3.6448 L27.9989999,22.0490667 L28,22.0490667 L28,32 Z M23.8614088,20.0181333 C23.5309223,19.6105242 22.9540812,19.5633836 22.5692242,19.9125333 C22.5392199,19.9392 19.5537934,22.5941333 13.9989999,22.5941333 C8.51321617,22.5941333 5.48178311,19.9584 5.4277754,19.9104 C5.04295119,19.5629428 4.46760991,19.6105095 4.13759108,20.0170667 C3.97913051,20.2124916 3.9004494,20.4673395 3.91904357,20.7249415 C3.93763774,20.9825435 4.05196575,21.2215447 4.23660523,21.3888 C4.37862552,21.5168 7.77411059,24.5386667 13.9989999,24.5386667 C20.2248893,24.5386667 23.6203743,21.5168 23.7623946,21.3888 C23.9467342,21.2215726 24.0608642,20.9827905 24.0794539,20.7254507 C24.0980436,20.4681109 24.0195551,20.2135019 23.8614088,20.0181333 Z"></path></svg>',
            'line' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M272.1 204.2v71.1c0 1.8-1.4 3.2-3.2 3.2h-11.4c-1.1 0-2.1-.6-2.6-1.3l-32.6-44v42.2c0 1.8-1.4 3.2-3.2 3.2h-11.4c-1.8 0-3.2-1.4-3.2-3.2v-71.1c0-1.8 1.4-3.2 3.2-3.2H219c1 0 2.1.5 2.6 1.4l32.6 44v-42.2c0-1.8 1.4-3.2 3.2-3.2h11.4c1.8-.1 3.3 1.4 3.3 3.1zm-82-3.2h-11.4c-1.8 0-3.2 1.4-3.2 3.2v71.1c0 1.8 1.4 3.2 3.2 3.2h11.4c1.8 0 3.2-1.4 3.2-3.2v-71.1c0-1.7-1.4-3.2-3.2-3.2zm-27.5 59.6h-31.1v-56.4c0-1.8-1.4-3.2-3.2-3.2h-11.4c-1.8 0-3.2 1.4-3.2 3.2v71.1c0 .9.3 1.6.9 2.2.6.5 1.3.9 2.2.9h45.7c1.8 0 3.2-1.4 3.2-3.2v-11.4c0-1.7-1.4-3.2-3.1-3.2zM332.1 201h-45.7c-1.7 0-3.2 1.4-3.2 3.2v71.1c0 1.7 1.4 3.2 3.2 3.2h45.7c1.8 0 3.2-1.4 3.2-3.2v-11.4c0-1.8-1.4-3.2-3.2-3.2H301v-12h31.1c1.8 0 3.2-1.4 3.2-3.2V234c0-1.8-1.4-3.2-3.2-3.2H301v-12h31.1c1.8 0 3.2-1.4 3.2-3.2v-11.4c-.1-1.7-1.5-3.2-3.2-3.2zM448 113.7V399c-.1 44.8-36.8 81.1-81.7 81H81c-44.8-.1-81.1-36.9-81-81.7V113c.1-44.8 36.9-81.1 81.7-81H367c44.8.1 81.1 36.8 81 81.7zm-61.6 122.6c0-73-73.2-132.4-163.1-132.4-89.9 0-163.1 59.4-163.1 132.4 0 65.4 58 120.2 136.4 130.6 19.1 4.1 16.9 11.1 12.6 36.8-.7 4.1-3.3 16.1 14.1 8.8 17.4-7.3 93.9-55.3 128.2-94.7 23.6-26 34.9-52.3 34.9-81.5z"></path></svg>',
            'wechat' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z"></path></svg>',
            'google-play' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M325.3 234.3L104.6 13l280.8 161.2-60.1 60.1zM47 0C34 6.8 25.3 19.2 25.3 35.3v441.3c0 16.1 8.7 28.5 21.7 35.3l256.6-256L47 0zm425.2 225.6l-58.9-34.1-65.7 64.5 65.7 64.5 60.1-34.1c18-14.3 18-46.5-1.2-60.8zM104.6 499l280.8-161.2-60.1-60.1L104.6 499z"></path></svg>',
            'app-store-ios' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zM127 384.5c-5.5 9.6-17.8 12.8-27.3 7.3-9.6-5.5-12.8-17.8-7.3-27.3l14.3-24.7c16.1-4.9 29.3-1.1 39.6 11.4L127 384.5zm138.9-53.9H84c-11 0-20-9-20-20s9-20 20-20h51l65.4-113.2-20.5-35.4c-5.5-9.6-2.2-21.8 7.3-27.3 9.6-5.5 21.8-2.2 27.3 7.3l8.9 15.4 8.9-15.4c5.5-9.6 17.8-12.8 27.3-7.3 9.6 5.5 12.8 17.8 7.3 27.3l-85.8 148.6h62.1c20.2 0 31.5 23.7 22.7 40zm98.1 0h-29l19.6 33.9c5.5 9.6 2.2 21.8-7.3 27.3-9.6 5.5-21.8 2.2-27.3-7.3-32.9-56.9-57.5-99.7-74-128.1-16.7-29-4.8-58 7.1-67.8 13.1 22.7 32.7 56.7 58.9 102h52c11 0 20 9 20 20 0 11.1-9 20-20 20z"></path></svg>',
            'android' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M89.6 204.5v115.8c0 15.4-12.1 27.7-27.5 27.7-15.3 0-30.1-12.4-30.1-27.7V204.5c0-15.1 14.8-27.5 30.1-27.5 15.1 0 27.5 12.4 27.5 27.5zm10.8 157c0 16.4 13.2 29.6 29.6 29.6h19.9l.3 61.1c0 36.9 55.2 36.6 55.2 0v-61.1h37.2v61.1c0 36.7 55.5 36.8 55.5 0v-61.1h20.2c16.2 0 29.4-13.2 29.4-29.6V182.1H100.4v179.4zm248-189.1H99.3c0-42.8 25.6-80 63.6-99.4l-19.1-35.3c-2.8-4.9 4.3-8 6.7-3.8l19.4 35.6c34.9-15.5 75-14.7 108.3 0L297.5 34c2.5-4.3 9.5-1.1 6.7 3.8L285.1 73c37.7 19.4 63.3 56.6 63.3 99.4zm-170.7-55.5c0-5.7-4.6-10.5-10.5-10.5-5.7 0-10.2 4.8-10.2 10.5s4.6 10.5 10.2 10.5c5.9 0 10.5-4.8 10.5-10.5zm113.4 0c0-5.7-4.6-10.5-10.2-10.5-5.9 0-10.5 4.8-10.5 10.5s4.6 10.5 10.5 10.5c5.6 0 10.2-4.8 10.2-10.5zm94.8 60.1c-15.1 0-27.5 12.1-27.5 27.5v115.8c0 15.4 12.4 27.7 27.5 27.7 15.4 0 30.1-12.4 30.1-27.7V204.5c0-15.4-14.8-27.5-30.1-27.5z"></path></svg>',
            'apple' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 376 512"><path fill="currentColor" d="M314.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C59.3 141.2 0 184.8 0 273.5c0 26.2 4.8 53.3 14.4 81.2 12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"></path></svg>',
            'comment-lines-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32zm32 264c0 4.4-3.6 8-8 8H136c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm96-96c0 4.4-3.6 8-8 8H136c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h240c4.4 0 8 3.6 8 8v16z"></path></svg>',
            'comment-lines-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M280 272H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h144c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8zm96-96H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h240c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8zM256 32C114.6 32 0 125.1 0 240c0 47.6 19.9 91.2 52.9 126.3C38 405.7 7 439.1 6.5 439.5c-6.6 7-8.4 17.2-4.6 26S14.4 480 24 480c61.5 0 110-25.7 139.1-46.3C192 442.8 223.2 448 256 448c141.4 0 256-93.1 256-208S397.4 32 256 32zm0 384c-28.3 0-56.3-4.3-83.2-12.8l-15.2-4.8-13 9.2c-23 16.3-58.5 35.3-102.6 39.6 12-15.1 29.8-40.4 40.8-69.6l7.1-18.7-13.7-14.6C47.3 313.7 32 277.6 32 240c0-97 100.5-176 224-176s224 79 224 176-100.5 176-224 176z"></path></svg>',
            'comment-dots-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32zM128 272c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"></path></svg>',
            'comment-dots-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M128 216c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm128 0c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm128 0c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zM256 32C114.6 32 0 125.1 0 240c0 47.6 19.9 91.2 52.9 126.3C38 405.7 7 439.1 6.5 439.5c-6.6 7-8.4 17.2-4.6 26S14.4 480 24 480c61.5 0 110-25.7 139.1-46.3C192 442.8 223.2 448 256 448c141.4 0 256-93.1 256-208S397.4 32 256 32zm0 384c-28.3 0-56.3-4.3-83.2-12.8l-15.2-4.8-13 9.2c-23 16.3-58.5 35.3-102.6 39.6 12-15.1 29.8-40.4 40.8-69.6l7.1-18.7-13.7-14.6C47.3 313.7 32 277.6 32 240c0-97 100.5-176 224-176s224 79 224 176-100.5 176-224 176z"></path></svg>',
            'comment-check-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32zm114.1 163.8l-131 130c-4.3 4.3-11.3 4.3-15.6-.1l-75.7-76.3c-4.3-4.3-4.2-11.3.1-15.6l26-25.8c4.3-4.3 11.3-4.2 15.6.1l42.1 42.5 97.2-96.4c4.3-4.3 11.3-4.2 15.6.1l25.8 26c4.2 4.3 4.2 11.3-.1 15.5z"></path></svg>',
            'comment-check-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M345.3 166.5c-1.6-1.6-3.6-2.3-5.7-2.3-2 0-4.1.8-5.7 2.3L226.5 273.9 178 225.4c-1.6-1.6-3.6-2.3-5.7-2.3-2 0-4.1.8-5.7 2.3l-11.3 11.3c-3.1 3.1-3.1 8.2 0 11.3l65.5 65.5c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3L356.7 189c3.1-3.1 3.1-8.2 0-11.3l-11.4-11.2zM256 32C114.6 32 0 125.1 0 240c0 47.6 19.9 91.2 52.9 126.3C38 405.7 7 439.1 6.5 439.5c-6.6 7-8.4 17.2-4.6 26S14.4 480 24 480c61.5 0 110-25.7 139.1-46.3C192 442.8 223.2 448 256 448c141.4 0 256-93.1 256-208S397.4 32 256 32zm0 384c-28.3 0-56.3-4.3-83.2-12.8l-15.2-4.8-13 9.2c-23 16.3-58.5 35.3-102.6 39.6 12-15.1 29.8-40.4 40.8-69.6l7.1-18.7-13.7-14.6C47.3 313.7 32 277.6 32 240c0-97 100.5-176 224-176s224 79 224 176-100.5 176-224 176z"></path></svg>',
            'comment-alt-smile-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 9.8 11.2 15.5 19.1 9.7L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zM320 133.2c14.8 0 26.8 12 26.8 26.8s-12 26.8-26.8 26.8-26.8-12-26.8-26.8 12-26.8 26.8-26.8zm-128 0c14.8 0 26.8 12 26.8 26.8s-12 26.8-26.8 26.8-26.8-12-26.8-26.8 12-26.8 26.8-26.8zm164.2 140.9C331.3 303.3 294.8 320 256 320c-38.8 0-75.3-16.7-100.2-45.9-5.8-6.7-5-16.8 1.8-22.5 6.7-5.7 16.8-5 22.5 1.8 18.8 22 46.5 34.6 75.8 34.6 29.4 0 57-12.6 75.8-34.7 5.8-6.7 15.9-7.5 22.6-1.8 6.8 5.8 7.6 15.9 1.9 22.6z"></path></svg>',
            'comment-alt-smile-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 7.1 5.8 12 12 12 2.4 0 4.9-.7 7.1-2.4L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zm32 352c0 17.6-14.4 32-32 32H293.3l-8.5 6.4L192 460v-76H64c-17.6 0-32-14.4-32-32V64c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v288zM331.8 237.3C313 259.4 285.4 272 256 272s-57-12.6-75.8-34.6c-5.7-6.7-15.8-7.4-22.5-1.8-6.8 5.8-7.5 15.8-1.8 22.6C180.7 287.3 217.2 304 256 304s75.3-16.7 100.2-45.9c5.8-6.7 4.9-16.8-1.8-22.6-6.7-5.7-16.8-4.9-22.6 1.8zM192 184c13.3 0 24-10.7 24-24s-10.7-24-24-24-24 10.7-24 24 10.7 24 24 24zm128 0c13.3 0 24-10.7 24-24s-10.7-24-24-24-24 10.7-24 24 10.7 24 24 24z"></path></svg>',
            'comment-alt-lines-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 9.8 11.2 15.5 19.1 9.7L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zM288 264c0 4.4-3.6 8-8 8H136c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm96-96c0 4.4-3.6 8-8 8H136c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h240c4.4 0 8 3.6 8 8v16z"></path></svg>',
            'comment-alt-lines-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 7.1 5.8 12 12 12 2.4 0 4.9-.7 7.1-2.4L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zm32 352c0 17.6-14.4 32-32 32H293.3l-8.5 6.4L192 460v-76H64c-17.6 0-32-14.4-32-32V64c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v288zM280 240H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h144c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8zm96-96H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h240c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8z"></path></svg>',
            'comment-alt-dots-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 9.8 11.2 15.5 19.1 9.7L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zM128 240c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"></path></svg>',
            'comment-alt-dots-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 7.1 5.8 12 12 12 2.4 0 4.9-.7 7.1-2.4L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zm32 352c0 17.6-14.4 32-32 32H293.3l-8.5 6.4L192 460v-76H64c-17.6 0-32-14.4-32-32V64c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v288zM128 184c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm128 0c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm128 0c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24z"></path></svg>',
            'comments-alt-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M416 224V64c0-35.3-28.7-64-64-64H64C28.7 0 0 28.7 0 64v160c0 35.3 28.7 64 64 64v54.2c0 8 9.1 12.6 15.5 7.8l82.8-62.1H352c35.3.1 64-28.6 64-63.9zm96-64h-64v64c0 52.9-43.1 96-96 96H192v64c0 35.3 28.7 64 64 64h125.7l82.8 62.1c6.4 4.8 15.5.2 15.5-7.8V448h32c35.3 0 64-28.7 64-64V224c0-35.3-28.7-64-64-64z"></path></svg>',
            'comments-alt-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M512 160h-96V64c0-35.3-28.7-64-64-64H64C28.7 0 0 28.7 0 64v160c0 35.3 28.7 64 64 64h32v52c0 7.1 5.8 12 12 12 2.4 0 4.9-.7 7.1-2.4L224 288h128c35.3 0 64-28.7 64-64v-32h96c17.6 0 32 14.4 32 32v160c0 17.6-14.4 32-32 32h-64v49.6l-80.2-45.4-7.3-4.2H256c-17.6 0-32-14.4-32-32v-96l-32 18.1V384c0 35.3 28.7 64 64 64h96l108.9 61.6c2.2 1.6 4.7 2.4 7.1 2.4 6.2 0 12-4.9 12-12v-52h32c35.3 0 64-28.7 64-64V224c0-35.3-28.7-64-64-64zm-128 64c0 17.6-14.4 32-32 32H215.6l-7.3 4.2-80.3 45.4V256H64c-17.6 0-32-14.4-32-32V64c0-17.6 14.4-32 32-32h288c17.6 0 32 14.4 32 32v160z"></path></svg>',
            'comments' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M416 192c0-88.4-93.1-160-208-160S0 103.6 0 192c0 34.3 14.1 65.9 38 92-13.4 30.2-35.5 54.2-35.8 54.5-2.2 2.3-2.8 5.7-1.5 8.7S4.8 352 8 352c36.6 0 66.9-12.3 88.7-25 32.2 15.7 70.3 25 111.3 25 114.9 0 208-71.6 208-160zm122 220c23.9-26 38-57.7 38-92 0-66.9-53.5-124.2-129.3-148.1.9 6.6 1.3 13.3 1.3 20.1 0 105.9-107.7 192-240 192-10.8 0-21.3-.8-31.7-1.9C207.8 439.6 281.8 480 368 480c41 0 79.1-9.2 111.3-25 21.8 12.7 52.1 25 88.7 25 3.2 0 6.1-1.9 7.3-4.8 1.3-2.9.7-6.3-1.5-8.7-.3-.3-22.4-24.2-35.8-54.5z"></path></svg>',
            'comments-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M532 386.2c27.5-27.1 44-61.1 44-98.2 0-80-76.5-146.1-176.2-157.9C368.4 72.5 294.3 32 208 32 93.1 32 0 103.6 0 192c0 37 16.5 71 44 98.2-15.3 30.7-37.3 54.5-37.7 54.9-6.3 6.7-8.1 16.5-4.4 25 3.6 8.5 12 14 21.2 14 53.5 0 96.7-20.2 125.2-38.8 9.1 2.1 18.4 3.7 28 4.8 31.5 57.5 105.5 98 191.8 98 20.8 0 40.8-2.4 59.8-6.8 28.5 18.5 71.6 38.8 125.2 38.8 9.2 0 17.5-5.5 21.2-14 3.6-8.5 1.9-18.3-4.4-25-.5-.4-22.6-24.2-37.9-54.9zM142.2 311l-11.4 7.4c-20.1 13.1-50.5 28.2-87.7 32.5 8.8-11.3 20.2-27.6 29.5-46.4L83 283.7l-16.5-16.3C50.7 251.9 32 226.2 32 192c0-70.6 79-128 176-128s176 57.4 176 128-79 128-176 128c-17.7 0-35.4-2-52.6-6l-13.2-3zm303 103.4l-11.4-7.4-13.2 3.1c-17.2 4-34.9 6-52.6 6-65.1 0-122-25.9-152.4-64.3C326.9 348.6 416 278.4 416 192c0-9.5-1.3-18.7-3.3-27.7C488.1 178.8 544 228.7 544 288c0 34.2-18.7 59.9-34.5 75.4L493 379.7l10.3 20.7c9.4 18.9 20.8 35.2 29.5 46.4-37.1-4.2-67.5-19.4-87.6-32.4zm-37.8-267.7c.1.2.1.4.2.6-.1-.2-.1-.4-.2-.6z"></path></svg>',
            'comment-smile-solid' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32zm64 133.2c14.8 0 26.8 12 26.8 26.8s-12 26.8-26.8 26.8-26.8-12-26.8-26.8 12-26.8 26.8-26.8zm-128 0c14.8 0 26.8 12 26.8 26.8s-12 26.8-26.8 26.8-26.8-12-26.8-26.8 12-26.8 26.8-26.8zm164.2 140.9C331.3 335.3 294.8 352 256 352c-38.8 0-75.3-16.7-100.2-45.9-5.8-6.7-5-16.8 1.8-22.5 6.7-5.7 16.8-5 22.5 1.8 18.8 22 46.5 34.6 75.8 34.6 29.4 0 57-12.6 75.8-34.7 5.8-6.7 15.9-7.5 22.6-1.8 6.8 5.8 7.6 15.9 1.9 22.6z"></path></svg>',
            'comment-smile-light' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 47.6 19.9 91.2 52.9 126.3C38 405.7 7 439.1 6.5 439.5c-6.6 7-8.4 17.2-4.6 26S14.4 480 24 480c61.5 0 110-25.7 139.1-46.3C192 442.8 223.2 448 256 448c141.4 0 256-93.1 256-208S397.4 32 256 32zm0 384c-28.3 0-56.3-4.3-83.2-12.8l-15.2-4.8-13 9.2c-23 16.3-58.5 35.3-102.6 39.6 12-15.1 29.8-40.4 40.8-69.6l7.1-18.7-13.7-14.6C47.3 313.7 32 277.6 32 240c0-97 100.5-176 224-176s224 79 224 176-100.5 176-224 176zm75.8-130.7C313 307.4 285.4 320 256 320s-57-12.6-75.8-34.6c-5.7-6.7-15.8-7.4-22.5-1.8-6.8 5.8-7.5 15.8-1.8 22.6C180.7 335.3 217.2 352 256 352s75.3-16.7 100.2-45.9c5.8-6.7 4.9-16.8-1.8-22.6-6.7-5.7-16.8-4.9-22.6 1.8zM192 216c13.3 0 24-10.7 24-24s-10.7-24-24-24-24 10.7-24 24 10.7 24 24 24zm128 0c13.3 0 24-10.7 24-24s-10.7-24-24-24-24 10.7-24 24 10.7 24 24 24z"></path></svg>',
            'hangouts' => '<svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="Canvas" transform="translate(-825 -308)"><g id="Vector"><use xlink:href="#path0_fill0123" transform="translate(825 308)" fill="currentColor"></use></g></g><defs><path fill="currentColor" id="path0_fill0123" d="M 19 4L 17 4L 17 13L 4 13L 4 15C 4 15.55 4.45 16 5 16L 16 16L 20 20L 20 5C 20 4.45 19.55 4 19 4ZM 15 10L 15 1C 15 0.45 14.55 0 14 0L 1 0C 0.45 0 0 0.45 0 1L 0 15L 4 11L 14 11C 14.55 11 15 10.55 15 10Z"></path></defs></svg>',
            'zalo' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 460.1 436.6"><path fill="currentColor" class="st0" d="M82.6 380.9c-1.8-.8-3.1-1.7-1-3.5 1.3-1 2.7-1.9 4.1-2.8 13.1-8.5 25.4-17.8 33.5-31.5 6.8-11.4 5.7-18.1-2.8-26.5C69 269.2 48.2 212.5 58.6 145.5 64.5 107.7 81.8 75 107 46.6c15.2-17.2 33.3-31.1 53.1-42.7 1.2-.7 2.9-.9 3.1-2.7-.4-1-1.1-.7-1.7-.7-33.7 0-67.4-.7-101 .2C28.3 1.7.5 26.6.6 62.3c.2 104.3 0 208.6 0 313 0 32.4 24.7 59.5 57 60.7 27.3 1.1 54.6.2 82 .1 2 .1 4 .2 6 .2H290c36 0 72 .2 108 0 33.4 0 60.5-27 60.5-60.3v-.6-58.5c0-1.4.5-2.9-.4-4.4-1.8.1-2.5 1.6-3.5 2.6-19.4 19.5-42.3 35.2-67.4 46.3-61.5 27.1-124.1 29-187.6 7.2-5.5-2-11.5-2.2-17.2-.8-8.4 2.1-16.7 4.6-25 7.1-24.4 7.6-49.3 11-74.8 6zm72.5-168.5c1.7-2.2 2.6-3.5 3.6-4.8 13.1-16.6 26.2-33.2 39.3-49.9 3.8-4.8 7.6-9.7 10-15.5 2.8-6.6-.2-12.8-7-15.2-3-.9-6.2-1.3-9.4-1.1-17.8-.1-35.7-.1-53.5 0-2.5 0-5 .3-7.4.9-5.6 1.4-9 7.1-7.6 12.8 1 3.8 4 6.8 7.8 7.7 2.4.6 4.9.9 7.4.8 10.8.1 21.7 0 32.5.1 1.2 0 2.7-.8 3.6 1-.9 1.2-1.8 2.4-2.7 3.5-15.5 19.6-30.9 39.3-46.4 58.9-3.8 4.9-5.8 10.3-3 16.3s8.5 7.1 14.3 7.5c4.6.3 9.3.1 14 .1 16.2 0 32.3.1 48.5-.1 8.6-.1 13.2-5.3 12.3-13.3-.7-6.3-5-9.6-13-9.7-14.1-.1-28.2 0-43.3 0zm116-52.6c-12.5-10.9-26.3-11.6-39.8-3.6-16.4 9.6-22.4 25.3-20.4 43.5 1.9 17 9.3 30.9 27.1 36.6 11.1 3.6 21.4 2.3 30.5-5.1 2.4-1.9 3.1-1.5 4.8.6 3.3 4.2 9 5.8 14 3.9 5-1.5 8.3-6.1 8.3-11.3.1-20 .2-40 0-60-.1-8-7.6-13.1-15.4-11.5-4.3.9-6.7 3.8-9.1 6.9zm69.3 37.1c-.4 25 20.3 43.9 46.3 41.3 23.9-2.4 39.4-20.3 38.6-45.6-.8-25-19.4-42.1-44.9-41.3-23.9.7-40.8 19.9-40 45.6zm-8.8-19.9c0-15.7.1-31.3 0-47 0-8-5.1-13-12.7-12.9-7.4.1-12.3 5.1-12.4 12.8-.1 4.7 0 9.3 0 14v79.5c0 6.2 3.8 11.6 8.8 12.9 6.9 1.9 14-2.2 15.8-9.1.3-1.2.5-2.4.4-3.7.2-15.5.1-31 .1-46.5z"/></svg>',
            'linked-in' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/></svg>',
            'linked-in2' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>',
            'zendesk' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path xmlns="http://www.w3.org/2000/svg" fill="currentColor" d="M12 8.2v14.5H0zM12 3c0 3.3-2.7 6-6 6S0 6.3 0 3h12zm2 19.7c0-3.3 2.7-6 6-6s6 2.7 6 6H14zm0-5.2V3h12z"/></svg>',
            'instagram' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" class=""></path></svg>',
            'open-book' => '<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 297.001 297.001" style="enable-background:new 0 0 297.001 297.001;" xml:space="preserve"><g><g><path style="fill: currentColor" d="M287.034,60.873l-20.819-0.001V39.321c0-4.934-3.61-9.126-8.49-9.856c-0.852-0.128-21.134-3.074-45.557,1.37 c-27.227,4.954-48.941,17.171-63.668,35.64c-14.728-18.469-36.442-30.686-63.668-35.64c-24.424-4.443-44.706-1.498-45.557-1.37 c-4.88,0.731-8.49,4.923-8.49,9.856v21.551H9.966C4.463,60.872,0,65.335,0,70.839v187.805c0,3.227,1.562,6.254,4.193,8.124 s6.004,2.35,9.051,1.288c0.748-0.259,75.431-25.747,131.12-0.345c2.628,1.199,5.645,1.199,8.273,0 c55.533-25.33,130.376,0.088,131.12,0.345c1.068,0.372,2.174,0.555,3.276,0.555c2.043,0,4.065-0.628,5.775-1.842 c2.631-1.87,4.193-4.897,4.193-8.124V70.84C297,65.336,292.538,60.873,287.034,60.873z M19.933,245.309V80.805h10.852v132.726 c0,2.896,1.267,5.646,3.458,7.539c2.191,1.893,5.105,2.742,7.969,2.319c0.55-0.08,43.846-6.024,75.478,15.679 C78.725,232.405,39.727,240.112,19.933,245.309z M138.534,230.08c-13.932-12.588-32.079-21.1-53.702-25.034 c-10.406-1.894-20.06-2.446-27.78-2.446c-2.292,0-4.414,0.049-6.333,0.126V48.473h-0.001c19.155-0.864,65.752,1.184,87.816,38.587 V230.08z M158.466,87.061c21.985-37.243,68.655-39.384,87.816-38.563v154.228c-8.383-0.338-20.62-0.136-34.114,2.32 c-21.623,3.934-39.77,12.445-53.702,25.034V87.061z M179.277,239.074c31.636-21.716,74.955-15.766,75.495-15.686 c2.871,0.431,5.783-0.413,7.981-2.305c2.198-1.894,3.462-4.65,3.462-7.552V80.806h10.852v164.503 C257.267,240.11,218.253,232.4,179.277,239.074z"/></g></g></svg>',
            'open-book-2' => '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 296.999 296.999" style="enable-background:new 0 0 296.999 296.999;" xml:space="preserve"><g><g><g><path style="fill: currentColor" d="M45.432,35.049c-0.008,0-0.017,0-0.025,0c-2.809,0-5.451,1.095-7.446,3.085c-2.017,2.012-3.128,4.691-3.128,7.543 v159.365c0,5.844,4.773,10.61,10.641,10.625c24.738,0.059,66.184,5.215,94.776,35.136V84.023c0-1.981-0.506-3.842-1.461-5.382 C115.322,40.849,70.226,35.107,45.432,35.049z"/><path style="fill: currentColor" d="M262.167,205.042V45.676c0-2.852-1.111-5.531-3.128-7.543c-1.995-1.99-4.639-3.085-7.445-3.085c-0.009,0-0.018,0-0.026,0 c-24.793,0.059-69.889,5.801-93.357,43.593c-0.955,1.54-1.46,3.401-1.46,5.382v166.779 c28.592-29.921,70.038-35.077,94.776-35.136C257.394,215.651,262.167,210.885,262.167,205.042z"/><path style="fill: currentColor" d="M286.373,71.801h-7.706v133.241c0,14.921-12.157,27.088-27.101,27.125c-20.983,0.05-55.581,4.153-80.084,27.344 c42.378-10.376,87.052-3.631,112.512,2.171c3.179,0.724,6.464-0.024,9.011-2.054c2.538-2.025,3.994-5.052,3.994-8.301V82.427 C297,76.568,292.232,71.801,286.373,71.801z"/><path style="fill: currentColor" d="M18.332,205.042V71.801h-7.706C4.768,71.801,0,76.568,0,82.427v168.897c0,3.25,1.456,6.276,3.994,8.301 c2.545,2.029,5.827,2.78,9.011,2.054c25.46-5.803,70.135-12.547,112.511-2.171c-24.502-23.19-59.1-27.292-80.083-27.342 C30.49,232.13,18.332,219.963,18.332,205.042z"/></g></g></g></svg>',
            'bars' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg>'
        );
    }
    
    public function getCurrentIP()
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && $_SERVER['HTTP_X_FORWARDED_FOR'] != '127.0.0.1') {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] != '127.0.0.1') {
            return $_SERVER['REMOTE_ADDR'];
        }
        if (isset($_SERVER['HTTP_X_REAL_IP']) && $_SERVER['HTTP_X_REAL_IP'] != '127.0.0.1') {
            return $_SERVER['HTTP_X_REAL_IP'];
        }
    }
    
    public function timezoneSelectOptions()
    {
        $list = timezone_identifiers_list();
        $res = array(
            'auto' => 'AUTO'
        );
        foreach ($list as $tz) {
            $res[$tz] = $tz;
        }
        return $res;
    }
    
    public function animationSelectOptions()
    {
        return array(
            'bounce' => array(
                    'id' => 'bounce',
                    'name' => 'Bounce',
                    'items' => array(
                            'bounceIn' => esc_html__('bounceIn', 'ar-contactus'),
                            'bounceInDown' => esc_html__('bounceInDown', 'ar-contactus'),
                            'bounceInLeft' => esc_html__('bounceInLeft', 'ar-contactus'),
                            'bounceInRight' => esc_html__('bounceInRight', 'ar-contactus'),
                            'bounceInUp' => esc_html__('bounceInUp', 'ar-contactus'),
                    )
            ),
            'fade' => array(
                    'id' => 'fade',
                    'name' => 'Fade',
                    'items' => array(
                            'fadeIn' => esc_html__('fadeIn', 'ar-contactus'),
                            'fadeInDown' => esc_html__('fadeInDown', 'ar-contactus'),
                            'fadeInDownBig' => esc_html__('fadeInDownBig', 'ar-contactus'),
                            'fadeInLeft' => esc_html__('fadeInLeft', 'ar-contactus'),
                            'fadeInLeftBig' => esc_html__('fadeInLeftBig', 'ar-contactus'),
                            'fadeInRight' => esc_html__('fadeInRight', 'ar-contactus'),
                            'fadeInRightBig' => esc_html__('fadeInRightBig', 'ar-contactus'),
                            'fadeInUp' => esc_html__('fadeInUp', 'ar-contactus'),
                            'fadeInUpBig' => esc_html__('fadeInUpBig', 'ar-contactus'),
                    )
            ),
            'flip' => array(
                    'id' => 'flip',
                    'name' => 'Flip',
                    'items' => array(
                            'flip' => esc_html__('flip', 'ar-contactus'),
                            'flipInX' => esc_html__('flipInX', 'ar-contactus'),
                            'flipInY' => esc_html__('flipInY', 'ar-contactus'),
                    )
            ),
            'lightSpeed' => array(
                    'id' => 'lightSpeed',
                    'name' => 'LightSpeed',
                    'items' => array(
                            'lightSpeedIn' => esc_html__('lightSpeedIn', 'ar-contactus'),
                    )
            ),
            'rotate' => array(
                    'id' => 'rotate',
                    'name' => 'Rotate',
                    'items' => array(
                            'rotateIn' => esc_html__('rotateIn', 'ar-contactus'),
                            'rotateInDownLeft' => esc_html__('rotateInDownLeft', 'ar-contactus'),
                            'rotateInDownRight' => esc_html__('rotateInDownRight', 'ar-contactus'),
                            'rotateInUpLeft' => esc_html__('rotateInUpLeft', 'ar-contactus'),
                            'rotateInUpRight' => esc_html__('rotateInUpRight', 'ar-contactus'),
                    )
            ),
            'slide' => array(
                    'id' => 'slide',
                    'name' => 'Slide',
                    'items' => array(
                            'slideInUp' => esc_html__('slideInUp', 'ar-contactus'),
                            'slideInDown' => esc_html__('slideInDown', 'ar-contactus'),
                            'slideInLeft' => esc_html__('slideInLeft', 'ar-contactus'),
                            'slideInRight' => esc_html__('slideInRight', 'ar-contactus'),
                    )
            ),
            'zoom' => array(
                    'id' => 'zoom',
                    'name' => 'Zoom',
                    'items' => array(
                            'zoomIn' => esc_html__('zoomIn', 'ar-contactus'),
                            'zoomInDown' => esc_html__('zoomInDown', 'ar-contactus'),
                            'zoomInLeft' => esc_html__('zoomInLeft', 'ar-contactus'),
                            'zoomInRight' => esc_html__('zoomInRight', 'ar-contactus'),
                            'zoomInUp' => esc_html__('zoomInUp', 'ar-contactus'),
                    )
            ),
            'roll' => array(
                    'id' => 'roll',
                    'name' => 'Roll',
                    'items' => array(
                            'rollIn' => esc_html__('rollIn', 'ar-contactus'),
                    )
            ),
        );
    }
    
    public function menuHeaderLayoutSelectOptions()
    {
        return array(
            'noicon' => __('Text only', 'ar-contactus'),
            'icon-left' => __('Icon left', 'ar-contactus'),
            'icon-center' => __('Icon center', 'ar-contactus')
        );
    }
    
    public function menuHeaderIconTypeSelectOptions()
    {
        return array(
            'svg' => __('Built-in SVG icon', 'ar-contactus'),
            //'fa5' => __('FontAwesome 5 icon', 'ar-contactus'),
            'img' => __('Uploaded image', 'ar-contactus')
        );
    }
    
    public function menuPopupStyleSelectOptions()
    {
        return array(
            'popup' => __('Popup', 'ar-contactus'),
            'sidebar' => __('Sidebar', 'ar-contactus'),
            'no-background' => __('No-background', 'ar-contactus')
        );
    }
    
    public function popupAnimationSelectOptions()
    {
        return array(
            'scale' => __('ScaleIn', 'ar-contactus'),
            'scaleout' => __('ScaleOut', 'ar-contactus'),
            'fadeindown' => __('FadeInDown', 'ar-contactus'),
            'fadeinup' => __('FadeInUp', 'ar-contactus'),
        );
    }
    
    public function sidebarAnimationSelectOptions()
    {
        return array(
            'elastic' => __('Elastic', 'ar-contactus'),
            'bubble' => __('Bubble', 'ar-contactus')
        );
    }
    
    public function menuStyleSelectOptions()
    {
        return array(
            'regular' => __('Regular', 'ar-contactus'),
            'style-1' => __('Style-1', 'ar-contactus')
        );
    }
    
    public function menuLayoutSelectOptions()
    {
        return array(
            'default' => __('Default', 'ar-contactus'),
            'personal' => __('Personal', 'ar-contactus')
        );
    }
    
    public function itemsAnimationSelectOptions()
    {
        return array(
            '-' => __('None', 'ar-contactus'),
            'downtoup' => __('Down to up', 'ar-contactus'),
            'uptodown' => __('Up to down', 'ar-contactus'),
            'fromaside' => __('From aside', 'ar-contactus')
        );
    }
    
    public function menuHeaderIconSvgSelectOptions()
    {
        $icons = self::getIcons();
        unset($icons['FontAwesome icon']);
        return $icons;
    }
    
    public function buttonIconSelectOptions()
    {
        $icons = self::getIcons();
        unset($icons['FontAwesome icon']);
        return $icons;
    }
    
    public function callbackAccessSelectOptions()
    {
        $roles = get_editable_roles();
        $array = array();
        foreach ($roles as $k => $v) {
            if ($k != 'administrator_') {
                $array[$k] = translate_user_role($v['name']);
            }
        }
        
        return $array;
    }
    
    public function modeSelectOptions()
    {
        return array(
            'regular' => __('Menu', 'ar-contactus'),
            'callback' => __('Callback only', 'ar-contactus'),
            'single' => __('Single menu item', 'ar-contactus'),
        );
    }
    
    public function skypeTypeSelectOptions()
    {
        return array(
            'user' => __('Skype user', 'ar-contactus'),
            'bot' => __('Skype bot', 'ar-contactus')
        );
    }
    
    public function menuSizeSelectOptions()
    {
        return array(
            'normal' => __('Normal', 'ar-contactus'),
            'large' => __('Large', 'ar-contactus'),
            'small' => __('Small', 'ar-contactus')
        );
    }
    
    public function buttonSizeSelectOptions()
    {
        return array(
            'huge' => __('Huge', 'ar-contactus'),
            'large' => __('Large', 'ar-contactus'),
            'medium' => __('Medium', 'ar-contactus'),
            'small' => __('Small', 'ar-contactus')
        );
    }
    
    public function positionSelectOptions()
    {
        return array(
            'left' => __('Left', 'ar-contactus'),
            'right' => __('Right', 'ar-contactus')
        );
    }
    
    public function itemBorderStyleSelectOptions()
    {
        return array(
            'none' => __('None', 'ar-contactus'),
            'solid' => __('Solid', 'ar-contactus'),
            'dashed' => __('Dashed', 'ar-contactus'),
        );
    }
    
    public function itemStyleSelectOptions()
    {
        return array(
            'rounded' => __('Icon in circle', 'ar-contactus'),
            'non-rounded' => __('Icon without circle', 'ar-contactus')
        );
    }
    
    public function buttonIconTypeSelectOptions()
    {
        return array(
            'built-in' => __('Built-in SVG', 'ar-contactus'),
            'uploaded' => __('Uploaded image', 'ar-contactus')
        );
    }
    
    public function promptPositionSelectOptions()
    {
        return array(
            'top' => __('Above the button', 'ar-contactus'),
            'side' => __('Side of the button', 'ar-contactus')
        );
    }
    
    public function showTypeSelectOptions()
    {
        return array(
            'menu_open' => __('When menu is opened', 'ar-contactus'),
            'page_load' => __('When page is loaded', 'ar-contactus')
        );
    }
    
    public static function getIcon($name)
    {
        if (self::isFontAwesomeStatic($name)) {
            return $name;
        }
        $icons = self::getIcons();
        return isset($icons[$name])? $icons[$name] : null;
    }
    
    public static function isFontAwesomeStatic($name)
    {
        return preg_match('/<i\s+class/is', $name);
    }
    
    public function rules()
    {
        return array(
            array(
                array(
                    'mobile',
                    'allowed_pages',
                    'timezone',
                    'pages',
                    'sandbox',
                    'allowed_ips',
                    'fa_css',
                    'minify',
                    'disable_init',
                    'hide_on_load',
                    //'disable_jquery',
                    'delay_init',
                    'ga_account',
                    'ga_script',
                    'ga_tracker',
                    'disable_callback_menu',
                    'disable_email_menu',
                    'callback_access',
                    'font',
                    'custom_css',
                    
                    'mode',
                    'online_badge',
                    'button_icon_type',
                    'button_icon',
                    'button_icon_img',
                    'button_color',
                    'button_size',
                    'button_icon_size',
                    'position',
                    'storefront_pos',
                    'animation',
                    'x_offset',
                    'y_offset',
                    'pulsate_speed',
                    'icon_speed',
                    'icon_animation_pause',
                    'text',
                    'title',
                    'description',
                    'label',
                    'drag',
                    
                    'menu_popup_style',
                    'popup_animation',
                    'sidebar_animation',
                    'menu_layout',
                    'icons_title',
                    'menu_style',
                    'item_style',
                    'items_animation',
                    'item_border_style',
                    'item_border_color',
                    
                    'menu_header_on',
                    'menu_header_layout',
                    'menu_header_icon_type',
                    'menu_header_icon_svg',
                    'menu_header_icon_fa5',
                    'menu_header_icon_img',
                    'menu_header',
                    'menu_subheader',
                    'header_close',
                    'header_close_bg',
                    'header_close_color',
                    
                    'menu_size',
                    'menu_bg',
                    'menu_color',
                    'menu_hbg',
                    'menu_hcolor',
                    'shadow_size',
                    'shadow_opacity',
                    'backdrop',
                    'auto_open',
                    
                    'enable_prompt',
                    'prompt_position',
                    'first_delay',
                    'loop',
                    'close_last',
                    'typing_time',
                    'message_time',
                    
                    'show_type',
                    'use_prompt',
                    'welcome_messages',
                    
                    'twilio',
                    'twilio_api_key',
                    'twilio_auth_token',
                    'twilio_phone',
                    // 'twilio_tophone' deprecated,
                    // 'twilio_message' deprecated,
                    
                    'tg',
                    'tg_token',
                    // 'tg_chat_id' deprecated,
                    // 'tg_text' deprecated,
                    
                    'email_img',
                    'callback_email_subject',
                    'callback_email_body',
                    'email_email_subject',
                    'email_email_body',
                    
                    'tawk_to_head',
                    'tawk_to_on',
                    'tawk_to_site_id',
                    'tawk_to_widget',
                    'tawk_to_userinfo',
                    // 'hr1',
                    
                    'crisp_head',
                    'crisp_on',
                    'crisp_site_id',
                    'hr2',
                    
                    'intercom_head',
                    'intercom_on',
                    'intercom_app_id',
                    'hr3',
                    
                    'fb_head',
                    'fb_alert',
                    'fb_on',
                    'fb_page_id',
                    'fb_init',
                    'fb_color',
                    'fb_lang',
                    'hr4',
                    
                    'vk_head',
                    'vk_page_id',
                    'vk_on',
                    'hr5',
                    
                    'zopim_head',
                    'zopim_id',
                    'zopim_on',
                    'zopim_userinfo',
                    'hr6',
                    
                    // 'skype_head' skype is removed,
                    // 'skype_id',
                    // 'skype_type',
                    // 'skype_on',
                    // 'skype_message_color',
                    // 'hr7',
                    
                    'zalo_head',
                    'zalo_id',
                    'zalo_on',
                    'zalo_welcome',
                    'hr8',
                    
                    'lhc_head',
                    'lhc_on',
                    'lhc_uri',
                    'hr9',
                    
                    'ss_head',
                    'ss_on',
                    'ss_userinfo',
                    'hr10',
                    
                    'lc_head',
                    'lc_on',
                    'lc_userinfo',
                    'hr11',
                    
                    'lcp_head',
                    'lcp_on',
                    'hr12',
                    
                    'lz_head',
                    'lz_on',
                    'hr13',
                    
                    'tidio_head',
                    'tidio_on',
                    'tidio_key',
                    'tidio_userinfo',
                    'hr14',
                    
                    'jivosite_head',
                    'jivosite_on',
                    'jivosite_id',
                    'jivosite_userinfo',
                    'hr15',
                    
                    'zoho_head',
                    'zoho_on',
                    'zoho_id',
                    'zoho_host',
                    'zoho_userinfo',
                    'hr16',
                    
                    'fc_head',
                    'fc_on',
                    'fc_token',
                    'fc_host',
                    'fc_userinfo',
                    'hr17',
                    
                    'phplive_head',
                    'phplive_on',
                    'phplive_src',
                    'phplive_userinfo',
                    'hr18',
                    
                    'paldesk_head',
                    'paldesk_on',
                    'paldesk_key',
                    //'paldesk_userinfo',
                    'hr19',
                    
                    // 'timeout' deprecated,
                    // 'message' deprecated,
                    // 'phone_placeholder' deprecated,
                    // 'phone_mask' deprecated,
                    'maskedinput',
                    // 'phone_mask_on' deprecated,
                    // 'proccess_message' deprecated,
                    // 'success_message' deprecated,
                    // 'fail_message' deprecated,
                    // 'btn_title' deprecated,
                    'onesignal',
                    'onesignal_app_id',
                    'onesignal_api_key',
                    // 'onesignal_title' deprecated,
                    // 'onesignal_message' deprecated,
                    'onesignal_alert',
                    'perfex_alert',
                    // 'name' deprecated,
                    
                    // 'name_validation' deprecated,
                    // 'name_max_len' deprecated,
                    // 'name_filter_laters' deprecated,
                    
                    // 'name_required' deprecated,
                    // 'name_title' deprecated,
                    // 'name_placeholder' deprecated,
                    
                    // 'email_field' deprecated,
                    // 'email_required' deprecated,
                    // 'email_title' deprecated,
                    // 'email_placeholder' deprecated,
                    
                    
                    // 'hhr1',
                    // 'hhr2',
                    // 'hhr3',
                    // 'hhr4',
                    
                    // 'gdpr' deprecated,
                    // 'gdpr_title' deprecated,
                    // 'email',
                    // 'email_list',
                    'recaptcha',
                    'key',
                    'secret',
                    'recaptcha_init',
                    'hide_recaptcha',
                    'recaptcha_error',
                    'recaptcha_treshold',
                    'perfex',
                    'perfex_url',
                    'perfex_token'
                ), 'safe'
            ),
            array(
                array(
                    'recaptcha_treshold'
                ), 'isZeroToOne'
            ),
            array(
                array(
                    'button_color',
                    'header_close_bg',
                    'header_close_color',
                    'menu_bg',
                    'menu_color',
                    'menu_subtitle_color',
                    'menu_hbg',
                    'menu_hcolor',
                    'menu_subtitle_hcolor'
                ), 'isColor'
            ),
            array(
                array(
                    //'timeout' deprecated,
                    'auto_open',
                    'delay_init',
                    //'popup_width' deprecated,
                    'menu_width',
                    'x_offset',
                    'y_offset',
                    'pulsate_speed',
                    'icon_speed',
                    'icon_animation_pause',
                    'show_after_close',
                    // 'close_timeout' deprecated,
                    'button_icon_size'
                ), 'isInt'
            )
        );
    }
    
    public function filters()
    {
        return array(
            array(
                array(
                    'gdpr_title',
                    'message',
                    'process_message',
                    'success_message',
                    'fail_message'
                ), 'stripSlashes'
            )
        );
    }
    
    
    
    public function isColor($value)
    {
        return empty($value) || preg_match('/^[a-fA-F0-9]{6}$/is', $value);
    }
    
    public function isZeroToOne($value)
    {
        return ($this->isInt($value) && $value == 1) || preg_match('/^0\.\d{1,2}$/is', $value);
    }
    
    public function isInt($value)
    {
        return ((string)(int)$value === (string)$value || $value === false);
    }
    
    public function attributeLabels()
    {
        return array(
            'mobile' => __('Enable on mobile', 'ar-contactus'),
            'allowed_pages' => __('Enable widget on pages', 'ar-contactus'),
            'pages' => __('Disable widget on pages', 'ar-contactus'),
            'sandbox' => __('Sandbox mode', 'ar-contactus'),
            'allowed_ips' => __('Allowed IPs', 'ar-contactus'),
            'timezone' => __('Server TimeZone', 'ar-contactus'),
            'fa_css' => __('Include FontAwesome CSS file', 'ar-contactus'),
            'minify' => __('Minify plugin JS output', 'ar-contactus'),
            'disable_init' => __('Disable plugin initialization', 'ar-contactus'),
            'hide_on_load' => __('Hide main widget button after page loads', 'ar-contactus'),
            'disable_jquery' => __('Disable jQuery initialization', 'ar-contactus'),
            'delay_init' => __('Delay plugin initialization', 'ar-contactus'),
            'ga_account' => __('Google Analytics account ID', 'ar-contactus'),
            'ga_script' => __('Include Google Analytics SDK script', 'ar-contactus'),
            'ga_tracker' => __('Create new Google Analytics tracker', 'ar-contactus'),
            'disable_callback_menu' => __('Disable "Callback" admin menu item', 'ar-contactus'),
            'disable_email_menu' => __('Disable "Email requests" admin menu item', 'ar-contactus'),
            'callback_access' => __('Access to callback list', 'ar-contactus'),
            'font' => __('Font', 'ar-contactus'),
            'custom_css' => __('Custom CSS rules', 'ar-contactus'),
            
            'mode' => __('Button mode', 'ar-contactus'),
            'online_badge' => __('Show online badge', 'ar-contactus'),
            'button_icon_type' => __('Button icon type', 'ar-contactus'),
            'button_icon' => __('Button icon', 'ar-contactus'),
            'button_icon_img' => __('Button icon', 'ar-contactus'),
            'button_color' => __('Color theme', 'ar-contactus'),
            'button_size' => __('Button size', 'ar-contactus'),
            'button_icon_size' => __('Button icon size', 'ar-contactus'),
            
            'position' => __('Position', 'ar-contactus'),
            'animation' => __('Appearing animation', 'ar-contactus'),
            'storefront_pos' => __('StoreFront button position number', 'ar-contactus'),
            'x_offset' => __('X-axis offset', 'ar-contactus'),
            'y_offset' => __('Y-axis offset', 'ar-contactus'),
            'pulsate_speed' => __('Pulsate speed', 'ar-contactus'),
            'icon_speed' => __('Icon slider speed', 'ar-contactus'),
            'icon_animation_pause' => __('Icon slider animation pause', 'ar-contactus'),
            'text' => __('Icon text', 'ar-contactus'),
            'title' => __('Button title', 'ar-contactus'),
            'description' => __('Button description', 'ar-contactus'),
            'label' => __('Button label', 'ar-contactus'),
            'drag' => __('Enable button drag', 'ar-contactus'),

            'enable_prompt' => __('Enable', 'ar-contactus'),
            'prompt_position' => __('Position', 'ar-contactus'),
            'first_delay' => __('Delay first message', 'ar-contactus'),
            'loop' => __('Loop mesages', 'ar-contactus'),
            'close_last' => __('Close last message', 'ar-contactus'),
            'typing_time' => __('Typing time', 'ar-contactus'),
            'message_time' => __('Message time', 'ar-contactus'),
            'show_after_close' => __('Show after closed', 'ar-contactus'),
            
            'show_type' => __('Show welcome messages', 'ar-contactus'),
            'use_prompt' => __('Use same settings as prompt messages', 'ar-contactus'),
            'welcome_messages' => __('Welcome messages', 'ar-contactus'),
            
            'menu_popup_style' => __('Popup style', 'ar-contactus'),
            'popup_animation' => __('Popup animation', 'ar-contactus'),
            'sidebar_animation' => __('Sidebar animation', 'ar-contactus'),
            'menu_style' => __('Menu style', 'ar-contactus'),
            'menu_layout' => __('Menu layout', 'ar-contactus'),
            'icons_title' => __('Icons block title', 'ar-contactus'),
            
            'item_style' => __('Items style', 'ar-contactus'),
            'items_animation' => __('Items animation', 'ar-contactus'),
            'item_border_style' => __('Items border', 'ar-contactus'),
            'item_border_color' => __('Items border color', 'ar-contactus'),
            'menu_header_on' => __('Show header', 'ar-contactus'),
            'menu_header' => __('Header text', 'ar-contactus'),
            'header_close' => __('Show close button in header', 'ar-contactus'),
            'menu_subtitle_color' => __('Item subtitle color', 'ar-contactus'),
            'menu_subtitle_hcolor' => __('Hovered item subtitle color', 'ar-contactus'),
            'header_close_bg' => __('Close button background color', 'ar-contactus'),
            'header_close_color' => __('Close button icon color', 'ar-contactus'),
            'shadow_size' => __('Shadow size', 'ar-contactus'),
            'shadow_opacity' => __('Shadow opacity', 'ar-contactus'),
            'auto_open' => __('Open menu automatically', 'ar-contactus'),
            'backdrop' => __('Backdrop', 'ar-contactus'),
            'menu_header_layout' => __('Header layout', 'ar-contactus'),
            'menu_header_icon_type' => __('Header icon type', 'ar-contactus'),
            'menu_header_icon_svg' => __('Header icon', 'ar-contactus'),
            'menu_header_icon_img' => __('Header icon', 'ar-contactus'),
            'menu_subheader' => __('Header subtitle', 'ar-contactus'),
            
            'menu_size' => __('Menu size', 'ar-contactus'),
            'menu_width' => __('Menu width', 'ar-contactus'),
            'menu_bg' => __('Menu background color', 'ar-contactus'),
            'menu_color' => __('Item title color', 'ar-contactus'),
            'menu_hbg' => __('Hovered item background color', 'ar-contactus'),
            'menu_hcolor' => __('Hovered item title color', 'ar-contactus'),
            
            'popup_width' => __('Popup desktop width', 'ar-contactus'),
            'timeout' => __('Countdown', 'ar-contactus'),
            'message' => __('Message', 'ar-contactus'),
            'phone_placeholder' => __('Phone field placeholder', 'ar-contactus'),
            'phone_mask' => __('Phone mask', 'ar-contactus'),
            'maskedinput' => __('Include jquery.maskedinput.min.js', 'ar-contactus'),
            'phone_mask_on' => __('Enable phone mask', 'ar-contactus'),
            'proccess_message' => __('Proccess message', 'ar-contactus'),
            'success_message' => __('Success message', 'ar-contactus'),
            'close_timeout' => __('Close callback popup timeout', 'ar-contactus'),
            'fail_message' => __('Fail message', 'ar-contactus'),
            'btn_title' => __('Button title', 'ar-contactus'),
            'onesignal' => __('Enable Onesignal integration', 'ar-contactus'),
            'onesignal_app_id' => __('Onesignal APP ID', 'ar-contactus'),
            'onesignal_api_key' => __('Onesignal Api Key', 'ar-contactus'),
            'onesignal_title' => __('Webpush message title', 'ar-contactus'),
            'onesignal_message' => __('Webpush message text', 'ar-contactus'),
            'onesignal_alert' => '',
            
            'name' => __('Show Name field', 'ar-contactus'),
            
            'name_validation' => __('Validate name value', 'ar-contactus'),
            'name_max_len' => __('Max lenght for name value', 'ar-contactus'),
            'name_filter_laters' => __('Enable only laters, numbers and spaces in name field.', 'ar-contactus'),
            
            'name_required' => __('Name field required', 'ar-contactus'),
            'name_title' => __('Name field title', 'ar-contactus'),
            'name_placeholder' => __('Name field placeholder', 'ar-contactus'),
            
            'email_field' => __('Show email field', 'ar-contactus'),
            'email_required' => __('Email field required', 'ar-contactus'),
            'email_title' => __('Email field title', 'ar-contactus'),
            'email_placeholder' => __('Email field placeholder', 'ar-contactus'),
            
            'gdpr' => __('Show GDPR checkbox', 'ar-contactus'),
            'gdpr_title' => __('GDPR checkbox title', 'ar-contactus'),
            'email' => __('Send email', 'ar-contactus'),
            'email_list' => __('Email list', 'ar-contactus'),
            'recaptcha' => __('Integrate with Google reCaptcha', 'ar-contactus'),
            'key' => __('Google reCaptcha Site Key', 'ar-contactus'),
            'secret' => __('Google reCaptcha Secret', 'ar-contactus'),
            'recaptcha_init' => __('Initialize Google reCaptcha', 'ar-contactus'),
            'hide_recaptcha' => __('Hide Google reCaptcha logo', 'ar-contactus'),
            'recaptcha_error' => __('reCaptcha error message', 'ar-contactus'),
            'recaptcha_treshold' => __('reCaptcha treshold value', 'ar-contactus'),
            
            'perfex' => __('Integrate with Perfex CRM', 'ar-contactus'),
            'perfex_url' => __('Perfex CRM API endpoint URL', 'ar-contactus'),
            'perfex_token' => __('Perfex CRM API token', 'ar-contactus'),
            
            'email_img' =>  __('Email logo', 'ar-contactus'),
            'callback_email_subject' =>  __('Callback request email subject', 'ar-contactus'),
            'callback_email_body' =>  __('Callback request email body', 'ar-contactus'),
            'email_email_subject' =>  __('Direct email subject', 'ar-contactus'),
            'email_email_body' =>  __('Direct email body', 'ar-contactus'),
            
            'tawk_to_head' => '',
            'hr1' => '',
            'tawk_to_widget' => __('Widget', 'ar-contactus'),
            'tawk_to_site_id' => __('Site ID', 'ar-contactus'),
            'tawk_to_userinfo' => __('Send customer info to Tawk.to', 'ar-contactus'),
            'crisp_head' => '',
            'crisp_site_id' => __('Site ID', 'ar-contactus'),
            'hr2' => '',
            'intercom_head' => '',
            'hr3' => '',
            'fb_head' => '',
            'fb_alert' => '',
            'hr4' => '',
            'intercom_app_id' => __('App ID', 'ar-contactus'),
            
            'tawk_to_on' => __('Enable', 'ar-contactus'),
            'crisp_on' => __('Enable', 'ar-contactus'),
            'intercom_on' => __('Enable', 'ar-contactus'),
            'fb_on' => __('Enable', 'ar-contactus'),
            
            'fb_page_id' => __('Facebook page ID', 'ar-contactus'),
            'fb_init' => __('Initilize Facebook SDK', 'ar-contactus'),
            'fb_color' => __('Color scheme', 'ar-contactus'),
            'fb_lang' => __('Language', 'ar-contactus'),
            
            'vk_head' => '',
            'vk_page_id' => __('VK page ID', 'ar-contactus'),
            'vk_on' => __('Enable', 'ar-contactus'),
            'hr5' => '',
            
            'zopim_head' => '',
            'zopim_id' => __('Widget ID', 'ar-contactus'),
            'zopim_on' => __('Enable', 'ar-contactus'),
            'zopim_userinfo' => __('Send customer info to Zendesk', 'ar-contactus'),
            'hr6' => '',
            
            'skype_head' => '',
            'skype_id' => __('Skype ID/Microsoft App ID', 'ar-contactus'),
            'skype_type' => __('Set receiver', 'ar-contactus'),
            'skype_on' => __('Enable', 'ar-contactus'),
            'skype_message_color' => __('Message color', 'ar-contactus'),
            'hr7' => '',
            
            'zalo_head' => '',
            'zalo_id' => __('Account ID', 'ar-contactus'),
            'zalo_on' => __('Enable', 'ar-contactus'),
            'zalo_welcome' => __('Welcome message', 'ar-contactus'),
            'zalo_height' => __('Height', 'ar-contactus'),
            'zalo_width' => __('Width', 'ar-contactus'),
            'hr8' => '',
            
            'lhc_head' => '',
            'lhc_on' => __('Enable', 'ar-contactus'),
            'lhc_uri' => __('Installation URL', 'ar-contactus'),
            'lhc_width' => __('Width', 'ar-contactus'),
            'lhc_height' => __('Height', 'ar-contactus'),
            'lhc_popup_height' => __('Popup width', 'ar-contactus'),
            'lhc_popup_width' => __('Popup height', 'ar-contactus'),
            
            'hr9' => '',
            
            'ss_head' => '',
            'ss_on' => __('Enable', 'ar-contactus'),
            'ss_key' => __('Smartsupp Key', 'ar-contactus'),
            'ss_userinfo' => __('Send customer info to smartsupp', 'ar-contactus'),
            'hr10' => '',
            
            'lc_head' => '',
            'lc_on' => __('Enable', 'ar-contactus'),
            'lc_key' => __('License ID', 'ar-contactus'),
            'lc_userinfo' => __('Send customer info to LiveChat', 'ar-contactus'),
            'hr11' => '',
            
            'lcp_head' => '',
            'lcp_on' => __('Enable', 'ar-contactus'),
            'lcp_uri' => __('Script URL', 'ar-contactus'),
            'hr12' => '',
            
            'lz_head' => '',
            'lz_id' => __('LiveZilla URL', 'ar-contactus'),
            'lz_on' => __('Enable', 'ar-contactus'),
            'hr13' => '',
            
            'tidio_head' => '',
            'tidio_on' => __('Enable', 'ar-contactus'),
            'tidio_key' => __('Public key', 'ar-contactus'),
            'tidio_userinfo' => __('Send customer info to Tidio', 'ar-contactus'),
            'hr14' => '',
            
            'jivosite_head' => '',
            'jivosite_on' => __('Enable', 'ar-contactus'),
            'jivosite_id' => __('Jivosite ID', 'ar-contactus'),
            'jivosite_userinfo' => __('Send customer info to Jivosite', 'ar-contactus'),
            'hr15' => '',
            
            'zoho_head' => '',
            'zoho_on' => __('Enable', 'ar-contactus'),
            'zoho_id' => __('Zoho SalesIQ widget ID', 'ar-contactus'),
            'zoho_host' => __('Zoho SalesIQ host', 'ar-contactus'),
            'zoho_userinfo' => __('Send customer info to SalesIQ', 'ar-contactus'),
            'hr16' => '',
            
            'fc_head' => '',
            'fc_on' => __('Enable', 'ar-contactus'),
            'fc_token' => __('FreshChat token', 'ar-contactus'),
            'fc_host' => __('FreshChat host', 'ar-contactus'),
            'fc_userinfo' => __('Send customer info to FreshChat', 'ar-contactus'),
            'hr17' => '',
            
            'phplive_head' => '',
            'phplive_on' => __('Enable', 'ar-contactus'),
            'phplive_src' => __('PhpLive widget URL', 'ar-contactus'),
            'phplive_userinfo' => __('Send customer info to PhpLive', 'ar-contactus'),
            'hr18' => '',

            'paldesk_head' => '',
            'paldesk_on' => __('Enable', 'ar-contactus'),
            'paldesk_key' => __('Paldesk API Key', 'ar-contactus'),
            'paldesk_userinfo' => __('Send customer info to Paldesk', 'ar-contactus'),
            'hr19' => '',
            
            'twilio' => __('Enable Twilio integration', 'ar-contactus'),
            'twilio_api_key' => __('Twilio API Key', 'ar-contactus'),
            'twilio_auth_token' => __('Twilio Auth Token', 'ar-contactus'),
            'twilio_phone' => __('Twilio phone', 'ar-contactus'),
            'twilio_tophone' => __('Send SMS to this phone', 'ar-contactus'),
            'twilio_message' => __('SMS text', 'ar-contactus'),
            
            'tg' => __('Enable telegram integration', 'ar-contactus'),
            'tg_token' => __('Telegram bot token', 'ar-contactus'),
            'tg_chat_id' => __('Telegram chat id', 'ar-contactus'),
            'tg_text' => __('Telegram message', 'ar-contactus'),
        );
    }
    
    public function fieldSuffix()
    {
        return array(
            'x_offset' => __('px', 'ar-contactus'),
            'y_offset' => __('px', 'ar-contactus'),
            'pulsate_speed' => __('ms', 'ar-contactus'),
            'icon_speed' => __('ms', 'ar-contactus'),
            'icon_animation_pause' => __('ms', 'ar-contactus'),
            'menu_border' => __('px', 'ar-contactus'),
            'timeout' => __('seconds', 'ar-contactus'),
            'first_delay' => __('ms', 'ar-contactus'),
            'typing_time' => __('ms', 'ar-contactus'),
            'message_time' => __('ms', 'ar-contactus'),
            'zalo_width' => __('px', 'ar-contactus'),
            'zalo_height' => __('px', 'ar-contactus'),
            'show_after_close' => __('minutes', 'ar-contactus'),
            'lhc_width' => __('px', 'ar-contactus'),
            'lhc_height' => __('px', 'ar-contactus'),
            'lhc_popup_height' => __('px', 'ar-contactus'),
            'lhc_popup_width' => __('px', 'ar-contactus'),
            'shadow_size' => __('px', 'ar-contactus'),
            'menu_width' => __('px', 'ar-contactus'),
            'popup_width' => __('px', 'ar-contactus'),
            'close_timeout' => __('seconds', 'ar-contactus'),
            'delay_init' => __('ms', 'ar-contactus'),
            'auto_open' => __('ms', 'ar-contactus'),
            'button_icon_size' => __('px', 'ar-contactus')
        );
    }
    
    public function attributeTypes()
    {
        return array(
            'mobile' => 'switch',
            'sandbox' => 'switch',
            'allowed_ips' => 'textarea',
            'timezone' => 'select',
            'fa_css' => 'switch',
            'minify' => 'switch',
            'disable_init' => 'switch',
            'hide_on_load' => 'switch',
            'disable_jquery' => 'switch',
            'ga_script' => 'switch',
            'ga_tracker' => 'switch',
            'disable_callback_menu' => 'switch',
            'disable_email_menu' => 'switch',
            'callback_access' => 'select',
            'custom_css' => 'textarea',
            
            'mode' => 'select',
            'online_badge' => 'switch',
            'button_icon_type' => 'select',
            'button_icon' => 'iconDropdown',
            'button_icon_img' => 'mediaImage',
            'button_color' => 'color',
            'button_size' => 'select',
            'button_icon_size' => 'text',
            'position' => 'select',
            'animation' => 'select',
            'x_offset' => 'text',
            'y_offset' => 'text',
            'pulsate_speed' => 'text',
            'icon_speed' => 'text',
            'icon_animation_pause' => 'text',
            'text' => 'text',
            'drag' => 'switch',
            
            'enable_prompt' => 'switch',
            'prompt_position' => 'select',
            'loop' => 'switch',
            'close_last' => 'switch',
            
            'show_type' => 'select',
            'use_prompt' => 'switch',
            'welcome_messages' => 'textarea',
            
            'menu_popup_style' => 'select',
            'popup_animation' => 'select',
            'sidebar_animation' => 'select',
            'menu_style' => 'select',
            'menu_layout' => 'select',
            'icons_title' => 'text',
            'item_style' => 'select',
            'items_animation' => 'select',
            'item_border_style' => 'select',
            'item_border_color' => 'color',
            'menu_header_on' => 'switch',
            
            'menu_header_layout' => 'select',
            'menu_header_icon_type' => 'select',
            'menu_header_icon_svg' => 'iconDropdown',
            'menu_header_icon_fa5' => 'text',
            'menu_header_icon_img' => 'mediaImage',
            'menu_header' => 'text',
            'menu_subheader' => 'text',
            
            'header_close' => 'switch',
            'menu_subtitle_color' => 'color',
            'menu_subtitle_hcolor' => 'color',
            'header_close_bg' => 'color',
            'header_close_color' => 'color',
            'shadow_size' => 'text',
            'shadow_opacity' => 'text',
    
            'menu_size' => 'select',
            'menu_bg' => 'color',
            'menu_color' => 'color',
            'menu_hbg' => 'color',
            'menu_hcolor' => 'color',
            'backdrop' => 'switch',
            
            'email_img' => 'mediaImage',
            'callback_email_subject' => 'text',
            'callback_email_body' => 'editor',
            'email_email_subject' => 'text',
            'email_email_body' => 'editor',
            
            'tawk_to_head' => 'html',
            'crisp_head' => 'html',
            'intercom_head' => 'html',
            'fb_head' => 'html',
            'fb_alert' => 'html',
            'hr1' => 'html',
            'hr2' => 'html',
            'hr3' => 'html',
            'hr4' => 'html',
            
            'hhr1' => 'html',
            'hhr2' => 'html',
            'hhr3' => 'html',
            'hhr4' => 'html',
            
            'tawk_to_on' => 'switch',
            'tawk_to_userinfo' => 'switch',
            'crisp_on' => 'switch',
            'intercom_on' => 'switch',
            'fb_on' => 'switch',
            
            'fb_init' => 'switch',
            'fb_color' => 'color',
            
            'vk_head' => 'html',
            'vk_on' => 'switch',
            'hr5' => 'html',
            
            'zopim_head' => 'html',
            'zopim_on' => 'switch',
            'zopim_userinfo' => 'switch',
            'hr6' => 'html',
            
            'skype_head' => 'html',
            'skype_on' => 'switch',
            'skype_type' => 'select',
            'skype_message_color' => 'color',
            'hr7' => 'html',
            
            'zalo_head' => 'html',
            'zalo_on' => 'switch',
            'hr8' => 'html',
            
            'lhc_head' => 'html',
            'lhc_on' => 'switch',
            'hr9' => 'html',
            
            'ss_head' => 'html',
            'ss_on' => 'switch',
            'ss_userinfo' => 'switch',
            'hr10' => 'html',
            
            'lc_head' => 'html',
            'lc_on' => 'switch',
            'lc_userinfo' => 'switch',
            'hr11' => 'html',
            
            'lcp_head' => 'html',
            'lcp_on' => 'switch',
            'hr12' => 'html',
            
            'lz_head' => 'html',
            'lz_on' => 'switch',
            'hr13' => 'html',
            
            'tidio_head' => 'html',
            'tidio_on' => 'switch',
            'tidio_userinfo' => 'switch',
            'hr14' => 'html',
            
            'jivosite_head' => 'html',
            'jivosite_on' => 'switch',
            'jivosite_userinfo' => 'switch',
            'hr15' => 'html',
            
            'zoho_head' => 'html',
            'zoho_on' => 'switch',
            'zoho_userinfo' => 'switch',
            'hr16' => 'html',
            
            'fc_head' => 'html',
            'fc_on' => 'switch',
            'fc_userinfo' => 'switch',
            'hr17' => 'html',
            
            'phplive_head' => 'html',
            'phplive_on' => 'switch',
            'phplive_userinfo' => 'switch',
            'hr18' => 'html',
            
            'paldesk_head' => 'html',
            'paldesk_on' => 'switch',
            'paldesk_userinfo' => 'switch',
            'hr19' => 'html',
            
            
            'twilio' => 'switch',
            'tg' => 'switch',
            'message' => 'textarea',
            'proccess_message' => 'textarea',
            'phone_mask_on' => 'switch',
            'maskedinput' => 'switch',
            'success_message' => 'textarea',
            'fail_message' => 'textarea',
            'onesignal' => 'switch',
            'name' => 'switch',
            
            'name_validation' => 'switch',
            'name_filter_laters' => 'switch',
            
            'email_field' => 'switch',
            'email_required' => 'switch',
            
            'name_required' => 'switch',
            'name_title' => 'text',
            'name_placeholder' => 'text',
            'gdpr' => 'switch',
            'gdpr_title' => 'textarea',
            'email_list' => 'textarea',
            'email' => 'switch',
            'recaptcha' => 'switch',
            'recaptcha_init' => 'switch',
            'hide_recaptcha' => 'switch',
            'pages' => 'textarea',
            'allowed_pages' => 'textarea',
            'onesignal_alert' => 'html',
            
            'perfex' => 'switch',
            'perfex_alert' => 'html',
            'perfex_url' => 'text',
            'perfex_token' => 'text',
        );
    }
    
    public function attributeDescriptions()
    {
        return array(
            'timeout' => __('Set to 0 to disable countdown.', 'ar-contactus'),
            'fa_css' => __('If you have already included FontAwesome CSS file, please disable this option', 'ar-contactus'),
            'ga_script' => __('If Google Analytics script is already added to your page or Google TagManager, please disable this option.', 'ar-contactus'),
            'font' => __('Enter "inherit" to use theme font-family. Leave empty to use default font.', 'ar-contactus'),
            
            'allowed_ips' => sprintf(__('One IP address per line. Your current IP %s', 'ar-contactus'), $this->getCurrentIP()),
            'timezone' => sprintf(__('Autodetected timezone: %s'), date_default_timezone_get()),
            'sandbox' => __('If enabled, module will be shown from allowed IPs only.', 'ar-contactus'),
            
            'disable_init' => __('To initialize plugin manually please call following JavaScript code when you need to initilize plugin contactUs.init(arcuOptions);', 'ar-contactus'),
            'minify' => __('This is experemental feature!', 'ar-contactus'),
            'delay_init' => __('Type 0 to initialize plugin without delay', 'ar-contactus'),
            'disable_jquery' => __('Enable this option only if you have already included jQuery to your site', 'ar-contactus'),
            
            'email_list' => __('One email per line.', 'ar-contactus'),
            'recaptcha' => __('You can use Google reCaptcha to prevent bots from sending callback requests. This module uses invisible reCaptcha V3', 'ar-contactus'),
            'key' => __('You can get your Key here https://g.co/recaptcha/v3', 'ar-contactus'),
            'secret' => __('You can get your Secret here https://g.co/recaptcha/v3', 'ar-contactus'),
            'recaptcha_init' => __('Please disable this option if you already have included Google reCaptcha script on your pages', 'ar-contactus'),
            'recaptcha_treshold' => __('From 0 to 1. Recomended value is 0.6', 'ar-contactus'),
            'recaptcha_error' => __('This message will be shown if reCaptcha is not valid', 'ar-contactus'),
            'allowed_pages' => __('You can enable widget on several pages. You can use relative or absolute URL. * symbol means any character. One URL per line. Leave this field blank to enable widget on all pages', 'ar-contactus'),
            'pages' => __('You can disable widget on several pages. You can use relative or absolute URL. * symbol means any character. One URL per line.', 'ar-contactus'),
            'icon_speed' => __('Type 0 here to disable button animation', 'ar-contactus'),
            'icon_animation_pause' => __('Pause beetwen slide icon animations loop', 'ar-contactus'),
            'shadow_size' => __('Shadow size applied to menu, callback form and prompt messages', 'ar-contactus'),
            'shadow_opacity' => __('From 0 to 1', 'ar-contactus'),
            
            'twilio_message' => __('{phone} token will be replaced to phone entered in callback request form. {site} token will be replaced to site domain. {name} token will be replaced to customer name. {referer} token will be replaced to page url which used for callback request. {email} token will be replaced to customer email address (if filled).', 'ar-contactus'),
            'onesignal_title' => __('{phone} token will be replaced to phone entered in callback request form. {site} token will be replaced to site domain. {name} token will be replaced to customer name. {referer} token will be replaced to page url which used for callback request. {email} token will be replaced to customer email address (if filled).', 'ar-contactus'),
            'onesignal_message' => __('{phone} token will be replaced to phone entered in callback request form. {site} token will be replaced to site domain. {name} token will be replaced to customer name. {referer} token will be replaced to page url which used for callback request. {email} token will be replaced to customer email address (if filled).', 'ar-contactus'),
            
            'twilio_phone' => __('Your Twilio phone in international format', 'ar-contactus'),
            'twilio_tophone' => __('SMS message will be send to this phone number. Use international format', 'ar-contactus'),
            
            'callback_email_body' =>  __('You can use ID of field as variable. For example: {phone} will be replaced to Phone field value. Also you can use built-in variables: {site} token will be replaced to site domain, {referer} token will be replaced to page url which used for callback request.', 'ar-contactus'),
            'email_email_body' =>  __('You can use ID of field as variable. For example: {phone} will be replaced to Phone field value. Also you can use built-in variables: {site} token will be replaced to site domain, {referer} token will be replaced to page url which used for direct email.', 'ar-contactus'),
            
            'fb_lang' => __('SDK locale. For example: en_EN, ru_RU, fr_FR. Default: en_EN', 'ar-contactus'),
            'fb_init' => __('You can disable Facebook SDK initialization to avoid conflicts with other modules that uses Facebook SDK', 'ar-contactus'),
            'phone_mask' => __('<b>X</b> means any number', 'ar-contactus'),
            'maskedinput' => __('Please toggle off this option if you already using maskedinput.js on your site', 'ar-contactus'),
            'vk_page_id' => __('You need to enable "Community messages" for your page in your page administration section', 'ar-contactus'),
            
            'tg_token' => __('To create new bot please write to bot t.me/botFather. Write /start then /newbot and follow instructions', 'ar-contactus'),
            'tg_chat_id' => __('Messages will be received to this chatID. To know your chatID please write to bot t.me/userinfobot. You can set few chatID comma-separated. Please note that each chatID must be subscribed to your bot to receive messages from the bot. To subscribe please find your bot in telegram and write /start to the bot.', 'ar-contactus'),
            'tg_text' => __('This message will be received to your telegram. {phone} token will be replaced to phone entered in callback request form. {name} token will be replaced to customer name. {referer} token will be replaced to page url which used for callback request. {email} token will be replaced to customer email address (if filled).', 'ar-contactus'),
            
            'zopim_id' => __('Zopim or Zendesk chat Widget ID', 'ar-contactus'),
            'show_after_close' => __('Show prompt messages again if visitor has closed prompts after this interval (in minutes). Type 0 if you want to show prompts on next session. Type -1 to show prompts after page reload', 'ar-contactus'),
            'welcome_messages' => __('Each message from new line', 'ar-contactus'),
            'menu_width' => __('Default 300. Type 0 for auto-width', 'ar-contactus'),
            'close_timeout' => __('If positive, callback popup will be closed after this interval. Leave 0 value if your want disable this feature.', 'ar-contactus'),
            
            'auto_open' => __('Menu will be opened automatically with delay after plugin initialization. Type 0 to disable this feature.', 'ar-contactus'),
            
            'zoho_host' => __('https://salesiq.zoho.eu or https://salesiq.zoho.com without leading slash', 'ar-contactus'),
        );
    }
    
    public function htmlFields()
    {
        return array(
            'tawk_to_head' => '<h3 class="section-head">' . __('Tawk.to integration', 'ar-contactus') . '</h3>',
            'hr1' => '<hr/>',
            'crisp_head' => '<h3 class="section-head">' . __('Crisp integration', 'ar-contactus') . '</h3>',
            'hr2' => '<hr/>',
            'intercom_head' => '<h3 class="section-head">' . __('Intercom integration', 'ar-contactus') . '</h3>',
            'hr3' => '<hr/>',
            'fb_head' => '<h3 class="section-head">' . __('Facebook customer chat', 'ar-contactus') . '</h3>',
            'hr4' => '<hr/>',
            'vk_head' => '<h3 class="section-head">' . __('VK community messages', 'ar-contactus') . '</h3>',
            'hr5' => '<hr/>',
            'zopim_head' => '<h3 class="section-head">' . __('Zendesk chat', 'ar-contactus') . '</h3>',
            'hr6' => '<hr/>',
            'skype_head' => '<h3 class="section-head">' . __('Skype Web Control', 'ar-contactus') . '</h3>',
            'hr7' => '<hr/>',
            'hr8' => '<hr/>',
            'hr9' => '<hr/>',
            'hr10' => '<hr/>',
            'hr11' => '<hr/>',
            'hr12' => '<hr/>',
            'hr13' => '<hr/>',
            'hr14' => '<hr/>',
            'hr15' => '<hr/>',
            'hr16' => '<hr/>',
            'hr17' => '<hr/>',
            'hr18' => '<hr/>',
            'hr19' => '<hr/>',
            
            'hhr1' => '<hr/>',
            'hhr2' => '<hr/>',
            'hhr3' => '<hr/>',
            'hhr4' => '<hr/>',
            
            'zoho_head' => '<h3 class="section-head">' . __('Zoho SalesIQ chat widget', 'ar-contactus') . '</h3>',
            'lz_head' => '<h3 class="section-head">' . __('LiveZilla chat widget', 'ar-contactus') . '</h3>',
            'zalo_head' => '<h3 class="section-head">' . __('Zalo chat widget', 'ar-contactus') . '</h3>',
            'fb_alert' => '<div class="ui orange message" style="display: block">' . __('Facebook customer chat requires HTTPS for full functionality', 'ar-contactus') . '</div>',
            'onesignal_alert' => '<div class="ui blue message" style="display: block">' . __('Onesignal requires HTTPS', 'ar-contactus') . '</div>',
            'perfex_alert' => '<div class="ui blue message" style="display: block">' . __('"REST API for Perfex CRM" module should be installed for Perfex CRM. More: ', 'ar-contactus') . '<a href="https://codecanyon.net/item/rest-api-for-perfex-crm/25278359" target="_blank">https://codecanyon.net/item/rest-api-for-perfex-crm/25278359</a></div>',
            'lhc_head' => '<h3 class="section-head">' . __('Live helper chat', 'ar-contactus') . '</h3>',
            'ss_head' => '<h3 class="section-head">' . __('Smartsupp', 'ar-contactus') . '</h3>',
            'lc_head' => '<h3 class="section-head">' . __('LiveChat', 'ar-contactus') . '</h3>',
            'lcp_head' => '<h3 class="section-head">' . __('LiveChat Pro', 'ar-contactus') . '</h3>',
            'tidio_head' => '<h3 class="section-head">' . __('Tidio', 'ar-contactus') . '</h3>',
            'jivosite_head' => '<h3 class="section-head">' . __('jivosite', 'ar-contactus') . '</h3>',
            'fc_head' => '<h3 class="section-head">' . __('FreshChat chat widget', 'ar-contactus') . '</h3>',
            'phplive_head' => '<h3 class="section-head">' . __('PhpLive chat widget', 'ar-contactus') . '</h3>',
            'paldesk_head' => '<h3 class="section-head">' . __('Paldesk chat widget', 'ar-contactus') . '</h3>'
        );
    }
    
    public function multipleSelects()
    {
        return array(
            'callback_access' => true
        );
    }
    
    public function validate($addErrors = true) {
        $valid = parent::validate($addErrors);
        
        $validValues = true;
        $values = null;
        foreach ($this->getAttributes() as $attr => $value) {
            switch ($this->getAttributeType($attr)) {
                case 'select':
                    if ($this->getGroupedSelect($attr)) {
                        $options = $this->getSelectOptions($attr);
                        $values = array();
                        foreach ($options as $k => $v) {
                            if ($v['items']){
                                $values = array_merge($values, array_keys($v['items']));
                            }
                        }
                    } else {
                        $values = array_keys($this->getSelectOptions($attr));  
                    }
                    break;
                case 'switch':
                    $values = array(0, 1);
                    break;
                default: 
                    $values = null;
            }
            if (is_array($values) && in_array(0, $values, true)) {
                $values[] = '0';
            }
            if (is_array($values) && in_array(1, $values, true)) {
                $values[] = '1';
            }
            if (is_array($values)) {
                if ($this->getAttributeType($attr) == 'select' && $this->getMultipleSelect($attr)) {
                    foreach ($value as $v) {
                        if (!in_array($v, $values)) {
                            $this->addError($attr, sprintf(__('Incorrect "%s" value', 'ar-contactus'), $this->getAttributeLabel($attr)));
                            $validValues = false;
                        }
                    }
                } else {
                    if (!in_array($value, $values, true)) {
                        $this->addError($attr, sprintf(__('Incorrect "%s" value', 'ar-contactus'), $this->getAttributeLabel($attr)));
                        $validValues = false;
                    }
                }
            }
        }
        return $valid && $validValues;
    }
    
    public function groupedSelects()
    {
        return array(
            'animation' => true
        );
    }
}
