# Anti-Spam Features for AR Contact Us Plugin

## Tổng quan

Plugin AR Contact Us đã được cập nhật với các tính năng chống spam mạnh mẽ để bảo vệ website khỏi các cuộc tấn công spam và bot tự động.

## C<PERSON><PERSON> tính năng Anti-Spam

### 1. Validation số điện thoại Việt Nam
- **Mô tả**: Chỉ chấp nhận số điện thoại Việt Nam hợp lệ
- **Định dạng hỗ trợ**: 
  - `0xxxxxxxxx` (10 số bắt đầu bằng 0)
  - `+84xxxxxxxxx` (có thể có dấu cách hoặc dấu chấm)
- **Regex pattern**: `/^(0|\+84)(\s|\.)?((3[2-9])|(5[689])|(7[06-9])|(8[1-689])|(9[0-46-9]))(\d)(\s|\.)?(\d{3})(\s|\.)?(\d{3})$/`
- **V<PERSON> dụ số hợp lệ**:
  - `0987654321`
  - `+84 987 654 321`
  - `0987.654.321`

### 2. Honeypot Field
- **Mô tả**: Trường ẩn để bẫy bot tự động
- **Cách hoạt động**: 
  - Trường input ẩn được thêm vào form
  - Người dùng thật không thể thấy và điền vào trường này
  - Bot tự động thường điền vào tất cả các trường, bao gồm cả trường ẩn
  - Nếu trường honeypot có giá trị, request sẽ bị từ chối
- **Triển khai**: Tự động được thêm vào tất cả các form

### 3. Rate Limiting
- **Mô tả**: Giới hạn số lần submit form từ cùng một IP
- **Giới hạn**:
  - **5 lần/giờ** từ cùng IP
  - **20 lần/ngày** từ cùng IP
- **Cách hoạt động**:
  - Theo dõi số lần submit từ mỗi IP
  - Tự động từ chối request khi vượt quá giới hạn
  - Dữ liệu được lưu trong WordPress options

### 4. IP Blocking
- **Mô tả**: Chặn IP address cụ thể
- **Tính năng**:
  - **Auto-blocking**: Tự động chặn IP sau 10 lần thất bại trong 1 giờ
  - **Manual blocking**: Admin có thể chặn IP thủ công
  - **Quản lý**: Xem danh sách IP bị chặn và bỏ chặn

## Cách sử dụng

### Truy cập Anti-Spam Settings
1. Đăng nhập WordPress Admin
2. Vào **Settings** → **Contact Us Anti-Spam**
3. Quản lý các IP bị chặn và xem thống kê

### Chặn IP thủ công
1. Vào trang Anti-Spam Settings
2. Nhập IP address cần chặn
3. Nhập lý do chặn (tùy chọn)
4. Click "Block IP"

### Bỏ chặn IP
1. Vào trang Anti-Spam Settings
2. Tìm IP trong danh sách
3. Click "Unblock" bên cạnh IP đó

### Dọn dẹp dữ liệu cũ
- Click "Clean Old Data" để xóa dữ liệu rate limiting cũ hơn 24 giờ

## Cấu hình nâng cao

### Thay đổi giới hạn Rate Limiting
Chỉnh sửa file `classes/ArContactUsAntiSpam.php`:

```php
const MAX_ATTEMPTS_PER_HOUR = 5;    // Thay đổi số lần/giờ
const MAX_ATTEMPTS_PER_DAY = 20;    // Thay đổi số lần/ngày
const AUTO_BLOCK_THRESHOLD = 10;    // Thay đổi ngưỡng auto-block
```

### Tùy chỉnh Regex số điện thoại
Chỉnh sửa method `isValidVietnamesePhone()` trong `classes/ArContactUsAntiSpam.php`

## Database Tables

Plugin sử dụng WordPress options để lưu trữ:
- `arcu_rate_limit_data`: Dữ liệu rate limiting
- `arcu_blocked_ips`: Danh sách IP bị chặn

## Troubleshooting

### IP bị chặn nhầm
1. Vào Anti-Spam Settings
2. Tìm IP trong danh sách blocked
3. Click "Unblock"

### Rate limit quá nghiêm ngặt
1. Chỉnh sửa constants trong `ArContactUsAntiSpam.php`
2. Hoặc click "Clean Old Data" để reset

### Form không hoạt động
1. Kiểm tra console browser có lỗi JavaScript không
2. Đảm bảo honeypot field được render đúng
3. Kiểm tra validation số điện thoại

## Bảo mật

- Tất cả input được sanitize
- Sử dụng WordPress nonce để bảo vệ CSRF
- IP được validate trước khi lưu
- Dữ liệu được escape khi output

## Performance

- Rate limiting data được clean up tự động
- Sử dụng WordPress options (cached)
- Minimal impact lên performance website

## Tương thích

- WordPress 3.7+
- PHP 5.6+
- Tương thích với tất cả theme
- Hoạt động với cả desktop và mobile
