<?php

class ArContactUsAntiSpam
{
    const RATE_LIMIT_OPTION = 'arcu_rate_limit_data';
    const BLOCKED_IPS_OPTION = 'arcu_blocked_ips';
    const MAX_ATTEMPTS_PER_HOUR = 5;
    const MAX_ATTEMPTS_PER_DAY = 20;
    const AUTO_BLOCK_THRESHOLD = 10; // Auto block after 10 attempts in an hour
    
    /**
     * Check if IP is rate limited
     * @param string $ip
     * @return bool
     */
    public static function isRateLimited($ip)
    {
        $rateLimitData = get_option(self::RATE_LIMIT_OPTION, array());
        $currentTime = time();
        $oneHourAgo = $currentTime - 3600;
        $oneDayAgo = $currentTime - 86400;
        
        if (!isset($rateLimitData[$ip])) {
            return false;
        }
        
        $ipData = $rateLimitData[$ip];
        
        // Clean old entries
        $ipData = array_filter($ipData, function($timestamp) use ($oneDayAgo) {
            return $timestamp > $oneDayAgo;
        });
        
        // Count attempts in last hour
        $attemptsLastHour = count(array_filter($ipData, function($timestamp) use ($oneHourAgo) {
            return $timestamp > $oneHourAgo;
        }));
        
        // Count attempts in last day
        $attemptsLastDay = count($ipData);
        
        // Update cleaned data
        $rateLimitData[$ip] = $ipData;
        update_option(self::RATE_LIMIT_OPTION, $rateLimitData);
        
        return $attemptsLastHour >= self::MAX_ATTEMPTS_PER_HOUR || $attemptsLastDay >= self::MAX_ATTEMPTS_PER_DAY;
    }
    
    /**
     * Record a form submission attempt
     * @param string $ip
     */
    public static function recordAttempt($ip)
    {
        $rateLimitData = get_option(self::RATE_LIMIT_OPTION, array());
        $currentTime = time();
        
        if (!isset($rateLimitData[$ip])) {
            $rateLimitData[$ip] = array();
        }
        
        $rateLimitData[$ip][] = $currentTime;
        
        // Check if IP should be auto-blocked
        $oneHourAgo = $currentTime - 3600;
        $attemptsLastHour = count(array_filter($rateLimitData[$ip], function($timestamp) use ($oneHourAgo) {
            return $timestamp > $oneHourAgo;
        }));
        
        if ($attemptsLastHour >= self::AUTO_BLOCK_THRESHOLD) {
            self::blockIP($ip, 'Auto-blocked for excessive attempts');
        }
        
        update_option(self::RATE_LIMIT_OPTION, $rateLimitData);
    }
    
    /**
     * Check if IP is blocked
     * @param string $ip
     * @return bool
     */
    public static function isIPBlocked($ip)
    {
        $blockedIPs = get_option(self::BLOCKED_IPS_OPTION, array());
        return isset($blockedIPs[$ip]);
    }
    
    /**
     * Block an IP address
     * @param string $ip
     * @param string $reason
     */
    public static function blockIP($ip, $reason = '')
    {
        $blockedIPs = get_option(self::BLOCKED_IPS_OPTION, array());
        $blockedIPs[$ip] = array(
            'blocked_at' => time(),
            'reason' => $reason
        );
        update_option(self::BLOCKED_IPS_OPTION, $blockedIPs);
    }
    
    /**
     * Unblock an IP address
     * @param string $ip
     */
    public static function unblockIP($ip)
    {
        $blockedIPs = get_option(self::BLOCKED_IPS_OPTION, array());
        if (isset($blockedIPs[$ip])) {
            unset($blockedIPs[$ip]);
            update_option(self::BLOCKED_IPS_OPTION, $blockedIPs);
        }
    }
    
    /**
     * Get blocked IPs list
     * @return array
     */
    public static function getBlockedIPs()
    {
        return get_option(self::BLOCKED_IPS_OPTION, array());
    }
    
    /**
     * Get user's IP address
     * @return string
     */
    public static function getUserIP()
    {
        $ipKeys = array('HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR');
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Clean old rate limit data (should be called periodically)
     */
    public static function cleanOldData()
    {
        $rateLimitData = get_option(self::RATE_LIMIT_OPTION, array());
        $oneDayAgo = time() - 86400;
        
        foreach ($rateLimitData as $ip => $attempts) {
            $rateLimitData[$ip] = array_filter($attempts, function($timestamp) use ($oneDayAgo) {
                return $timestamp > $oneDayAgo;
            });
            
            if (empty($rateLimitData[$ip])) {
                unset($rateLimitData[$ip]);
            }
        }
        
        update_option(self::RATE_LIMIT_OPTION, $rateLimitData);
    }
    
    /**
     * Validate Vietnamese phone number
     * @param string $phone
     * @return bool
     */
    public static function isValidVietnamesePhone($phone)
    {
        if (empty($phone)) {
            return false;
        }
        // Vietnamese phone number regex pattern
        return preg_match('/^(0|\+84)(\s|\.)?((3[2-9])|(5[689])|(7[06-9])|(8[1-689])|(9[0-46-9]))(\d)(\s|\.)?(\d{3})(\s|\.)?(\d{3})$/', $phone);
    }
}
