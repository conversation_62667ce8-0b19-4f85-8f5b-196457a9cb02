<?php
/**
 * Test file for Anti-Spam features
 * This file can be used to test the anti-spam functionality
 * 
 * Usage: Include this file in your WordPress environment and run the tests
 */

// Make sure this is run in WordPress environment
if (!defined('ABSPATH')) {
    die('This file must be run within WordPress environment');
}

// Load the anti-spam class
require_once(plugin_dir_path(__FILE__) . 'classes/ArContactUsAntiSpam.php');

class ArContactUsAntiSpamTest
{
    public static function runAllTests()
    {
        echo "<h2>AR Contact Us Anti-Spam Tests</h2>";
        
        self::testVietnamesePhoneValidation();
        self::testRateLimiting();
        self::testIPBlocking();
        self::testHoneypotValidation();
        
        echo "<h3>All tests completed!</h3>";
    }
    
    public static function testVietnamesePhoneValidation()
    {
        echo "<h3>Testing Vietnamese Phone Validation</h3>";
        
        $validPhones = array(
            '0987654321',
            '+84987654321',
            '0987.654.321',
            '+84 987 654 321',
            '0123456789',
            '+84123456789'
        );
        
        $invalidPhones = array(
            '123456789',      // Too short
            '01234567890',    // Too long
            '0187654321',     // Invalid prefix
            '+85987654321',   // Wrong country code
            'abcdefghij',     // Non-numeric
            '0987-654-321',   // Invalid separator
            ''                // Empty
        );
        
        echo "<h4>Valid phones (should pass):</h4>";
        foreach ($validPhones as $phone) {
            $result = ArContactUsAntiSpam::isValidVietnamesePhone($phone);
            echo "<p>$phone: " . ($result ? '<span style="color:green">PASS</span>' : '<span style="color:red">FAIL</span>') . "</p>";
        }
        
        echo "<h4>Invalid phones (should fail):</h4>";
        foreach ($invalidPhones as $phone) {
            $result = ArContactUsAntiSpam::isValidVietnamesePhone($phone);
            echo "<p>'$phone': " . (!$result ? '<span style="color:green">PASS</span>' : '<span style="color:red">FAIL</span>') . "</p>";
        }
    }
    
    public static function testRateLimiting()
    {
        echo "<h3>Testing Rate Limiting</h3>";
        
        $testIP = '*************';
        
        // Clean any existing data for test IP
        $rateLimitData = get_option(ArContactUsAntiSpam::RATE_LIMIT_OPTION, array());
        unset($rateLimitData[$testIP]);
        update_option(ArContactUsAntiSpam::RATE_LIMIT_OPTION, $rateLimitData);
        
        echo "<h4>Testing rate limit for IP: $testIP</h4>";
        
        // Test initial state
        $isLimited = ArContactUsAntiSpam::isRateLimited($testIP);
        echo "<p>Initial state: " . (!$isLimited ? '<span style="color:green">NOT LIMITED</span>' : '<span style="color:red">LIMITED</span>') . "</p>";
        
        // Record multiple attempts
        for ($i = 1; $i <= 6; $i++) {
            ArContactUsAntiSpam::recordAttempt($testIP);
            $isLimited = ArContactUsAntiSpam::isRateLimited($testIP);
            echo "<p>After $i attempts: " . ($isLimited ? '<span style="color:orange">LIMITED</span>' : '<span style="color:green">NOT LIMITED</span>') . "</p>";
        }
        
        // Clean up test data
        $rateLimitData = get_option(ArContactUsAntiSpam::RATE_LIMIT_OPTION, array());
        unset($rateLimitData[$testIP]);
        update_option(ArContactUsAntiSpam::RATE_LIMIT_OPTION, $rateLimitData);
    }
    
    public static function testIPBlocking()
    {
        echo "<h3>Testing IP Blocking</h3>";
        
        $testIP = '*************';
        
        // Clean any existing data for test IP
        ArContactUsAntiSpam::unblockIP($testIP);
        
        echo "<h4>Testing IP blocking for: $testIP</h4>";
        
        // Test initial state
        $isBlocked = ArContactUsAntiSpam::isIPBlocked($testIP);
        echo "<p>Initial state: " . (!$isBlocked ? '<span style="color:green">NOT BLOCKED</span>' : '<span style="color:red">BLOCKED</span>') . "</p>";
        
        // Block IP
        ArContactUsAntiSpam::blockIP($testIP, 'Test blocking');
        $isBlocked = ArContactUsAntiSpam::isIPBlocked($testIP);
        echo "<p>After blocking: " . ($isBlocked ? '<span style="color:orange">BLOCKED</span>' : '<span style="color:red">FAIL - NOT BLOCKED</span>') . "</p>";
        
        // Unblock IP
        ArContactUsAntiSpam::unblockIP($testIP);
        $isBlocked = ArContactUsAntiSpam::isIPBlocked($testIP);
        echo "<p>After unblocking: " . (!$isBlocked ? '<span style="color:green">NOT BLOCKED</span>' : '<span style="color:red">FAIL - STILL BLOCKED</span>') . "</p>";
    }
    
    public static function testHoneypotValidation()
    {
        echo "<h3>Testing Honeypot Validation</h3>";
        
        // Load form class
        require_once(plugin_dir_path(__FILE__) . 'classes/ArContactUsLoader.php');
        ArContactUsLoader::loadModel('ArContactUsConfigForms');
        
        $formsConfig = new ArContactUsConfigForms();
        $form = $formsConfig->getForm('email');
        
        if (!$form) {
            echo "<p><span style='color:red'>ERROR: Could not load email form</span></p>";
            return;
        }
        
        echo "<h4>Testing honeypot field validation</h4>";
        
        // Test with empty honeypot (should pass)
        $validData = array(
            'name' => 'Test User',
            'phone' => '0987654321',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'honeypot' => ''
        );
        
        $result = $form->validate($validData);
        echo "<p>Empty honeypot: " . ($result ? '<span style="color:green">PASS</span>' : '<span style="color:red">FAIL</span>') . "</p>";
        
        // Test with filled honeypot (should fail)
        $invalidData = array(
            'name' => 'Test User',
            'phone' => '0987654321',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'honeypot' => 'bot filled this'
        );
        
        $result = $form->validate($invalidData);
        echo "<p>Filled honeypot: " . (!$result ? '<span style="color:green">PASS (Correctly rejected)</span>' : '<span style="color:red">FAIL (Should be rejected)</span>') . "</p>";
        
        if (!$result) {
            $errors = $form->getErrors();
            if (isset($errors['honeypot'])) {
                echo "<p>Honeypot error message: " . implode(', ', $errors['honeypot']) . "</p>";
            }
        }
    }
    
    public static function testGetUserIP()
    {
        echo "<h3>Testing Get User IP</h3>";
        
        $ip = ArContactUsAntiSpam::getUserIP();
        echo "<p>Current IP: <strong>$ip</strong></p>";
        
        // Test with various headers
        $originalHeaders = array();
        $testHeaders = array(
            'HTTP_CF_CONNECTING_IP' => '*******',
            'HTTP_X_FORWARDED_FOR' => '*******, **********',
            'REMOTE_ADDR' => '***********'
        );
        
        foreach ($testHeaders as $header => $value) {
            if (isset($_SERVER[$header])) {
                $originalHeaders[$header] = $_SERVER[$header];
            }
            $_SERVER[$header] = $value;
        }
        
        $testIP = ArContactUsAntiSpam::getUserIP();
        echo "<p>Test IP with headers: <strong>$testIP</strong></p>";
        
        // Restore original headers
        foreach ($testHeaders as $header => $value) {
            if (isset($originalHeaders[$header])) {
                $_SERVER[$header] = $originalHeaders[$header];
            } else {
                unset($_SERVER[$header]);
            }
        }
    }
}

// Run tests if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    ArContactUsAntiSpamTest::runAllTests();
}
